# RBAC Frontend Implementation

This document describes the frontend implementation of the Role-Based Access Control (RBAC) system.

## Overview

The RBAC frontend provides a comprehensive interface for managing roles, permissions, and user assignments. It includes:

- **Roles Management**: Create, edit, delete, and manage role permissions
- **Permissions Management**: View all available system permissions
- **User Roles Assignment**: Assign and manage roles for users

## Components Structure

### Main Page
- `frontend/src/routes/_layout/rbac.tsx` - Main RBAC page with tabs

### Roles Management
- `frontend/src/components/RBAC/RolesManagement.tsx` - Main roles table and management
- `frontend/src/components/RBAC/AddRole.tsx` - Create new roles
- `frontend/src/components/RBAC/EditRole.tsx` - Edit existing roles
- `frontend/src/components/RBAC/DeleteRole.tsx` - Delete roles
- `frontend/src/components/RBAC/ManageRolePermissions.tsx` - Assign/remove permissions to/from roles
- `frontend/src/components/RBAC/RoleActionsMenu.tsx` - Actions menu for each role
- `frontend/src/components/RBAC/PendingRoles.tsx` - Loading skeleton for roles

### Permissions Management
- `frontend/src/components/RBAC/PermissionsManagement.tsx` - View all permissions

### User Roles Management
- `frontend/src/components/RBAC/UserRolesManagement.tsx` - Main user roles table
- `frontend/src/components/RBAC/AssignUserRole.tsx` - Assign roles to users
- `frontend/src/components/RBAC/ManageUserRoles.tsx` - Manage user's roles
- `frontend/src/components/RBAC/ViewUserPermissions.tsx` - View user's effective permissions
- `frontend/src/components/RBAC/UserRoleActions.tsx` - Actions menu for each user

### API Client
- `frontend/src/client/types.gen.rbac.ts` - TypeScript types for RBAC
- `frontend/src/client/sdk.gen.rbac.ts` - API service functions

## Features

### Roles Management
- **Create Role**: Add new roles with descriptions and active status
- **Edit Role**: Update role descriptions and status
- **Delete Role**: Remove roles (with confirmation)
- **Manage Permissions**: Assign/remove permissions to/from roles with categorized view
- **Role Status**: Toggle active/inactive status

### Permissions Management
- **View Permissions**: Display all available permissions categorized by resource
- **Permission Details**: Show permission descriptions and creation dates
- **Color Coding**: Visual indicators for different permission types

### User Roles Assignment
- **View Users**: List all users with their current roles
- **Assign Roles**: Add roles to users with dropdown selection
- **Remove Roles**: Remove roles from users with confirmation
- **View Permissions**: See effective permissions for each user
- **Search Users**: Filter users by name or email
- **Superuser Indicator**: Special badge for superusers

## Permission Categories

The system organizes permissions into logical categories:

1. **User Management**: create_user, read_user, update_user, delete_user
2. **School Management**: create_school, read_school, update_school, delete_school
3. **Security**: view_threats, manage_threats, configure_cameras
4. **System**: system_config, view_logs, manage_roles
5. **Academic**: manage_classes, view_students, manage_curriculum

## Role Color Coding

- **Admin**: Red
- **Teachers**: Blue
- **Management**: Purple
- **Security**: Orange
- **IT**: Green

## Access Control

The RBAC pages are only accessible to superusers. The navigation menu item is conditionally shown based on user permissions.

## Usage

### Accessing RBAC
1. Login as a superuser
2. Navigate to the RBAC section from the sidebar
3. Use the tabs to switch between different management areas

### Managing Roles
1. Go to "Roles Management" tab
2. Click "Add Role" to create new roles
3. Use the actions menu (⋮) to edit, manage permissions, or delete roles
4. In "Manage Permissions", check/uncheck permissions by category

### Assigning User Roles
1. Go to "User Roles Assignment" tab
2. Find the user in the table
3. Use the actions menu (⋮) to assign roles, manage existing roles, or view permissions
4. Use the search box to filter users

### Viewing Permissions
1. Go to "Permissions Management" tab to see all available permissions
2. Or use "View Permissions" in the user actions menu to see effective permissions for a specific user

## API Integration

The frontend uses the RBAC API endpoints:

- `GET /api/v1/rbac/roles/` - List roles
- `POST /api/v1/rbac/roles/` - Create role
- `PATCH /api/v1/rbac/roles/{id}` - Update role
- `DELETE /api/v1/rbac/roles/{id}` - Delete role
- `GET /api/v1/rbac/permissions/` - List permissions
- `POST /api/v1/rbac/users/{id}/roles` - Assign role to user
- `DELETE /api/v1/rbac/users/{id}/roles/{role_id}` - Remove role from user
- `GET /api/v1/rbac/users/{id}/roles` - Get user roles
- `GET /api/v1/rbac/users/{id}/permissions` - Get user permissions
- `POST /api/v1/rbac/roles/{id}/permissions` - Assign permission to role
- `DELETE /api/v1/rbac/roles/{id}/permissions/{permission_id}` - Remove permission from role

## Error Handling

- Form validation with real-time feedback
- API error handling with user-friendly messages
- Loading states for all async operations
- Confirmation dialogs for destructive actions

## Responsive Design

- Mobile-friendly responsive design
- Collapsible tables on smaller screens
- Touch-friendly buttons and interactions
- Optimized for both desktop and mobile use

## Future Enhancements

- Bulk role assignment
- Role templates
- Permission inheritance
- Audit logs for role changes
- Export/import role configurations
