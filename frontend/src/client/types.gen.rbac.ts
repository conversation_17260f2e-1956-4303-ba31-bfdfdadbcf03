// RBAC types for frontend client
// These types should match the backend RBAC models

export type RoleType = "admin" | "teachers" | "management" | "security" | "it"

export type PermissionType =
  | "create_user"
  | "read_user"
  | "update_user"
  | "delete_user"
  | "create_school"
  | "read_school"
  | "update_school"
  | "delete_school"
  | "view_threats"
  | "manage_threats"
  | "configure_cameras"
  | "system_config"
  | "view_logs"
  | "manage_roles"
  | "manage_classes"
  | "view_students"
  | "manage_curriculum"

export type RoleCreate = {
  name: RoleType
  description?: string | null
  is_active?: boolean
}

export type RoleUpdate = {
  description?: string | null
  is_active?: boolean | null
}

export type RolePublic = {
  name: RoleType
  description?: string | null
  is_active: boolean
  id: string
  created_at: string
  updated_at?: string | null
}

export type RolesPublic = {
  data: Array<RolePublic>
  count: number
}

export type PermissionCreate = {
  name: PermissionType
  description?: string | null
  resource?: string | null
}

export type PermissionUpdate = {
  description?: string | null
  resource?: string | null
}

export type PermissionPublic = {
  name: PermissionType
  description?: string | null
  resource?: string | null
  id: string
  created_at: string
}

export type PermissionsPublic = {
  data: Array<PermissionPublic>
  count: number
}

export type UserRoleAssign = {
  user_id: string
  role_id: string
}

export type UserRoleRemove = {
  user_id: string
  role_id: string
}

export type RolePermissionAssign = {
  role_id: string
  permission_id: string
}

export type RolePermissionRemove = {
  role_id: string
  permission_id: string
}

// API request/response types
export type RbacReadRolesData = {
  skip?: number
  limit?: number
}

export type RbacReadRolesResponse = RolesPublic

export type RbacCreateRoleData = {
  requestBody: RoleCreate
}

export type RbacCreateRoleResponse = RolePublic

export type RbacReadRoleData = {
  roleId: string
}

export type RbacReadRoleResponse = RolePublic

export type RbacUpdateRoleData = {
  roleId: string
  requestBody: RoleUpdate
}

export type RbacUpdateRoleResponse = RolePublic

export type RbacDeleteRoleData = {
  roleId: string
}

export type RbacDeleteRoleResponse = { message: string }

export type RbacReadPermissionsData = {
  skip?: number
  limit?: number
}

export type RbacReadPermissionsResponse = PermissionsPublic

export type RbacCreatePermissionData = {
  requestBody: PermissionCreate
}

export type RbacCreatePermissionResponse = PermissionPublic

export type RbacAssignUserRoleData = {
  userId: string
  requestBody: UserRoleAssign
}

export type RbacAssignUserRoleResponse = { message: string }

export type RbacRemoveUserRoleData = {
  userId: string
  roleId: string
}

export type RbacRemoveUserRoleResponse = { message: string }

export type RbacGetUserRolesData = {
  userId: string
}

export type RbacGetUserRolesResponse = RolesPublic

export type RbacGetUserPermissionsData = {
  userId: string
}

export type RbacGetUserPermissionsResponse = {
  permissions: Array<PermissionType>
}

export type RbacAssignRolePermissionData = {
  roleId: string
  requestBody: RolePermissionAssign
}

export type RbacAssignRolePermissionResponse = { message: string }

export type RbacRemoveRolePermissionData = {
  roleId: string
  permissionId: string
}

export type RbacRemoveRolePermissionResponse = { message: string }

export type RbacGetRolePermissionsData = {
  roleId: string
}

export type RbacGetRolePermissionsResponse = {
  permissions: Array<PermissionType>
}

export type RbacInitializeData = {}

export type RbacInitializeResponse = { message: string }

// Enhanced UserPublic type with roles
export type UserPublicWithRoles = {
  email: string
  is_active: boolean
  is_superuser: boolean
  full_name?: string | null
  id: string
  roles: Array<RolePublic>
}
