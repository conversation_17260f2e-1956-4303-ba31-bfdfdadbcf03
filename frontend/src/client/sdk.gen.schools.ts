// Manually added service for Schools API
// This should match the backend API endpoints

import { OpenAPI } from "./core/OpenAPI"
import { request as __request } from "./core/request"
import { CancelablePromise } from "./core/CancelablePromise"
import {
  SchoolsReadSchoolsData,
  SchoolsReadSchoolsResponse,
  SchoolsCreateSchoolData,
  SchoolsCreateSchoolResponse,
  SchoolsReadSchoolData,
  SchoolsReadSchoolResponse,
  SchoolsUpdateSchoolData,
  SchoolsUpdateSchoolResponse,
  SchoolsDeleteSchoolData,
  SchoolsDeleteSchoolResponse,
} from "./types.gen.schools"

export class SchoolsService {
  /**
   * Read Schools
   * Retrieve schools.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @returns SchoolsPublic Successful Response
   * @throws ApiError
   */
  public static readSchools(
    data: SchoolsReadSchoolsData = {},
  ): CancelablePromise<SchoolsReadSchoolsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/schools/",
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create School
   * Create new school.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns SchoolPublic Successful Response
   * @throws ApiError
   */
  public static createSchool(
    data: SchoolsCreateSchoolData,
  ): CancelablePromise<SchoolsCreateSchoolResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/schools/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read School
   * Get school by ID.
   * @param data The data for the request.
   * @param data.id
   * @returns SchoolPublic Successful Response
   * @throws ApiError
   */
  public static readSchool(
    data: SchoolsReadSchoolData,
  ): CancelablePromise<SchoolsReadSchoolResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: `/api/v1/schools/${data.id}`,
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update School
   * Update a school.
   * @param data The data for the request.
   * @param data.id
   * @param data.requestBody
   * @returns SchoolPublic Successful Response
   * @throws ApiError
   */
  public static updateSchool(
    data: SchoolsUpdateSchoolData,
  ): CancelablePromise<SchoolsUpdateSchoolResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: `/api/v1/schools/${data.id}`,
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete School
   * Delete a school.
   * @param data The data for the request.
   * @param data.id
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteSchool(
    data: SchoolsDeleteSchoolData,
  ): CancelablePromise<SchoolsDeleteSchoolResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: `/api/v1/schools/${data.id}`,
      errors: {
        422: "Validation Error",
      },
    })
  }
}
