// This file is auto-generated by @hey-api/openapi-ts

import type { CancelablePromise } from "./core/CancelablePromise"
import { OpenAPI } from "./core/OpenAPI"
import { request as __request } from "./core/request"
import type {
  BuildingsReadBuildingsData,
  BuildingsReadBuildingsResponse,
  BuildingsCreateBuildingData,
  BuildingsCreateBuildingResponse,
  BuildingsReadBuildingData,
  BuildingsReadBuildingResponse,
  BuildingsUpdateBuildingData,
  BuildingsUpdateBuildingResponse,
  BuildingsDeleteBuildingData,
  BuildingsDeleteBuildingResponse,
  FloorsReadFloorsData,
  FloorsReadFloorsResponse,
  FloorsCreateFloorData,
  FloorsCreateFloorResponse,
  FloorsReadFloorData,
  FloorsReadFloorResponse,
  FloorsUpdateFloorData,
  FloorsUpdateFloorResponse,
  FloorsDeleteFloorData,
  FloorsDeleteFloorResponse,
  ItemsReadItemsData,
  ItemsReadItemsResponse,
  ItemsCreateItemData,
  ItemsCreateItemResponse,
  ItemsReadItemData,
  ItemsReadItemResponse,
  ItemsUpdateItemData,
  ItemsUpdateItemResponse,
  ItemsDeleteItemData,
  ItemsDeleteItemResponse,
  LoginLoginAccessTokenData,
  LoginLoginAccessTokenResponse,
  LoginTestTokenResponse,
  LoginRecoverPasswordData,
  LoginRecoverPasswordResponse,
  LoginResetPasswordData,
  LoginResetPasswordResponse,
  LoginRecoverPasswordHtmlContentData,
  LoginRecoverPasswordHtmlContentResponse,
  PrivateCreateUserData,
  PrivateCreateUserResponse,
  RbacReadRolesData,
  RbacReadRolesResponse,
  RbacCreateRoleData,
  RbacCreateRoleResponse,
  RbacReadRoleData,
  RbacReadRoleResponse,
  RbacUpdateRoleData,
  RbacUpdateRoleResponse,
  RbacDeleteRoleData,
  RbacDeleteRoleResponse,
  RbacReadPermissionsData,
  RbacReadPermissionsResponse,
  RbacCreatePermissionData,
  RbacCreatePermissionResponse,
  RbacAssignUserRoleData,
  RbacAssignUserRoleResponse,
  RbacGetUserRolesEndpointData,
  RbacGetUserRolesEndpointResponse,
  RbacRemoveUserRoleData,
  RbacRemoveUserRoleResponse,
  RbacGetUserPermissionsEndpointData,
  RbacGetUserPermissionsEndpointResponse,
  RbacAssignRolePermissionData,
  RbacAssignRolePermissionResponse,
  RbacGetRolePermissionsEndpointData,
  RbacGetRolePermissionsEndpointResponse,
  RbacRemoveRolePermissionData,
  RbacRemoveRolePermissionResponse,
  RbacInitializeRbacResponse,
  SchoolsReadSchoolsData,
  SchoolsReadSchoolsResponse,
  SchoolsCreateSchoolData,
  SchoolsCreateSchoolResponse,
  SchoolsReadSchoolData,
  SchoolsReadSchoolResponse,
  SchoolsUpdateSchoolData,
  SchoolsUpdateSchoolResponse,
  SchoolsDeleteSchoolData,
  SchoolsDeleteSchoolResponse,
  ThreatDetectionReadThreatDetectionsData,
  ThreatDetectionReadThreatDetectionsResponse,
  ThreatDetectionReadThreatDetectionData,
  ThreatDetectionReadThreatDetectionResponse,
  ThreatDetectionUpdateThreatDetectionData,
  ThreatDetectionUpdateThreatDetectionResponse,
  ThreatDetectionDeleteThreatDetectionData,
  ThreatDetectionDeleteThreatDetectionResponse,
  ThreatDetectionGetThreatDetectionInterfaceResponse,
  UsersReadUsersData,
  UsersReadUsersResponse,
  UsersCreateUserData,
  UsersCreateUserResponse,
  UsersReadUserMeResponse,
  UsersDeleteUserMeResponse,
  UsersUpdateUserMeData,
  UsersUpdateUserMeResponse,
  UsersUpdatePasswordMeData,
  UsersUpdatePasswordMeResponse,
  UsersRegisterUserData,
  UsersRegisterUserResponse,
  UsersReadUserByIdData,
  UsersReadUserByIdResponse,
  UsersUpdateUserData,
  UsersUpdateUserResponse,
  UsersDeleteUserData,
  UsersDeleteUserResponse,
  UtilsTestEmailData,
  UtilsTestEmailResponse,
  UtilsHealthCheckResponse,
} from "./types.gen"

export class BuildingsService {
  /**
   * Read Buildings
   * Retrieve buildings.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @param data.includeDeleted
   * @param data.schoolId
   * @returns BuildingsPublic Successful Response
   * @throws ApiError
   */
  public static readBuildings(
    data: BuildingsReadBuildingsData = {},
  ): CancelablePromise<BuildingsReadBuildingsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/buildings/",
      query: {
        skip: data.skip,
        limit: data.limit,
        include_deleted: data.includeDeleted,
        school_id: data.schoolId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Building
   * Create new building.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns BuildingPublic Successful Response
   * @throws ApiError
   */
  public static createBuilding(
    data: BuildingsCreateBuildingData,
  ): CancelablePromise<BuildingsCreateBuildingResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/buildings/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Building
   * Get building by ID.
   * @param data The data for the request.
   * @param data.buildingId
   * @returns BuildingPublic Successful Response
   * @throws ApiError
   */
  public static readBuilding(
    data: BuildingsReadBuildingData,
  ): CancelablePromise<BuildingsReadBuildingResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/buildings/{building_id}",
      path: {
        building_id: data.buildingId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Building
   * Update a building.
   * @param data The data for the request.
   * @param data.buildingId
   * @param data.requestBody
   * @returns BuildingPublic Successful Response
   * @throws ApiError
   */
  public static updateBuilding(
    data: BuildingsUpdateBuildingData,
  ): CancelablePromise<BuildingsUpdateBuildingResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/buildings/{building_id}",
      path: {
        building_id: data.buildingId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Building
   * Soft delete a building.
   * @param data The data for the request.
   * @param data.buildingId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteBuilding(
    data: BuildingsDeleteBuildingData,
  ): CancelablePromise<BuildingsDeleteBuildingResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/buildings/{building_id}",
      path: {
        building_id: data.buildingId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class FloorsService {
  /**
   * Read Floors
   * Retrieve floors.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @param data.buildingId
   * @param data.includeDeleted
   * @returns FloorsPublic Successful Response
   * @throws ApiError
   */
  public static readFloors(
    data: FloorsReadFloorsData = {},
  ): CancelablePromise<FloorsReadFloorsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/floors/",
      query: {
        skip: data.skip,
        limit: data.limit,
        building_id: data.buildingId,
        include_deleted: data.includeDeleted,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Floor
   * Create new floor.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns FloorPublic Successful Response
   * @throws ApiError
   */
  public static createFloor(
    data: FloorsCreateFloorData,
  ): CancelablePromise<FloorsCreateFloorResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/floors/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Floor
   * Get floor by ID.
   * @param data The data for the request.
   * @param data.floorId
   * @returns FloorPublic Successful Response
   * @throws ApiError
   */
  public static readFloor(
    data: FloorsReadFloorData,
  ): CancelablePromise<FloorsReadFloorResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/floors/{floor_id}",
      path: {
        floor_id: data.floorId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Floor
   * Update a floor.
   * @param data The data for the request.
   * @param data.floorId
   * @param data.requestBody
   * @returns FloorPublic Successful Response
   * @throws ApiError
   */
  public static updateFloor(
    data: FloorsUpdateFloorData,
  ): CancelablePromise<FloorsUpdateFloorResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/floors/{floor_id}",
      path: {
        floor_id: data.floorId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Floor
   * Soft delete a floor.
   * @param data The data for the request.
   * @param data.floorId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteFloor(
    data: FloorsDeleteFloorData,
  ): CancelablePromise<FloorsDeleteFloorResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/floors/{floor_id}",
      path: {
        floor_id: data.floorId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Upload Floor Plan
   * Upload floor plan image for a floor.
   * @param data The data for the request.
   * @param data.floorId
   * @param data.formData
   * @returns FloorPublic Successful Response
   * @throws ApiError
   */
  public static uploadFloorPlan(
    data: FloorsUploadFloorPlanData,
  ): CancelablePromise<FloorsUploadFloorPlanResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/floors/{floor_id}/upload-floor-plan",
      path: {
        floor_id: data.floorId,
      },
      formData: data.formData,
      mediaType: "multipart/form-data",
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class ItemsService {
  /**
   * Read Items
   * Retrieve items.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @returns ItemsPublic Successful Response
   * @throws ApiError
   */
  public static readItems(
    data: ItemsReadItemsData = {},
  ): CancelablePromise<ItemsReadItemsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/items/",
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Item
   * Create new item.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns ItemPublic Successful Response
   * @throws ApiError
   */
  public static createItem(
    data: ItemsCreateItemData,
  ): CancelablePromise<ItemsCreateItemResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/items/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Item
   * Get item by ID.
   * @param data The data for the request.
   * @param data.id
   * @returns ItemPublic Successful Response
   * @throws ApiError
   */
  public static readItem(
    data: ItemsReadItemData,
  ): CancelablePromise<ItemsReadItemResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/items/{id}",
      path: {
        id: data.id,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Item
   * Update an item.
   * @param data The data for the request.
   * @param data.id
   * @param data.requestBody
   * @returns ItemPublic Successful Response
   * @throws ApiError
   */
  public static updateItem(
    data: ItemsUpdateItemData,
  ): CancelablePromise<ItemsUpdateItemResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/v1/items/{id}",
      path: {
        id: data.id,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Item
   * Delete an item.
   * @param data The data for the request.
   * @param data.id
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteItem(
    data: ItemsDeleteItemData,
  ): CancelablePromise<ItemsDeleteItemResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/items/{id}",
      path: {
        id: data.id,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class LoginService {
  /**
   * Login Access Token
   * OAuth2 compatible token login, get an access token for future requests
   * @param data The data for the request.
   * @param data.formData
   * @returns Token Successful Response
   * @throws ApiError
   */
  public static loginAccessToken(
    data: LoginLoginAccessTokenData,
  ): CancelablePromise<LoginLoginAccessTokenResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/login/access-token",
      formData: data.formData,
      mediaType: "application/x-www-form-urlencoded",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Test Token
   * Test access token
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static testToken(): CancelablePromise<LoginTestTokenResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/login/test-token",
    })
  }

  /**
   * Recover Password
   * Password Recovery
   * @param data The data for the request.
   * @param data.email
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static recoverPassword(
    data: LoginRecoverPasswordData,
  ): CancelablePromise<LoginRecoverPasswordResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/password-recovery/{email}",
      path: {
        email: data.email,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Reset Password
   * Reset password
   * @param data The data for the request.
   * @param data.requestBody
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static resetPassword(
    data: LoginResetPasswordData,
  ): CancelablePromise<LoginResetPasswordResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/reset-password/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Recover Password Html Content
   * HTML Content for Password Recovery
   * @param data The data for the request.
   * @param data.email
   * @returns string Successful Response
   * @throws ApiError
   */
  public static recoverPasswordHtmlContent(
    data: LoginRecoverPasswordHtmlContentData,
  ): CancelablePromise<LoginRecoverPasswordHtmlContentResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/password-recovery-html-content/{email}",
      path: {
        email: data.email,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class PrivateService {
  /**
   * Create User
   * Create a new user.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static createUser(
    data: PrivateCreateUserData,
  ): CancelablePromise<PrivateCreateUserResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/private/users/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class RbacService {
  /**
   * Read Roles
   * Retrieve roles.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @returns RolesPublic Successful Response
   * @throws ApiError
   */
  public static readRoles(
    data: RbacReadRolesData = {},
  ): CancelablePromise<RbacReadRolesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/rbac/roles/",
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Role
   * Create new role.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns RolePublic Successful Response
   * @throws ApiError
   */
  public static createRole(
    data: RbacCreateRoleData,
  ): CancelablePromise<RbacCreateRoleResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/rbac/roles/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Role
   * Get role by ID.
   * @param data The data for the request.
   * @param data.roleId
   * @returns RolePublic Successful Response
   * @throws ApiError
   */
  public static readRole(
    data: RbacReadRoleData,
  ): CancelablePromise<RbacReadRoleResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/rbac/roles/{role_id}",
      path: {
        role_id: data.roleId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Role
   * Update role.
   * @param data The data for the request.
   * @param data.roleId
   * @param data.requestBody
   * @returns RolePublic Successful Response
   * @throws ApiError
   */
  public static updateRole(
    data: RbacUpdateRoleData,
  ): CancelablePromise<RbacUpdateRoleResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/rbac/roles/{role_id}",
      path: {
        role_id: data.roleId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Role
   * Delete role.
   * @param data The data for the request.
   * @param data.roleId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteRole(
    data: RbacDeleteRoleData,
  ): CancelablePromise<RbacDeleteRoleResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/rbac/roles/{role_id}",
      path: {
        role_id: data.roleId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Permissions
   * Retrieve permissions.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @returns PermissionsPublic Successful Response
   * @throws ApiError
   */
  public static readPermissions(
    data: RbacReadPermissionsData = {},
  ): CancelablePromise<RbacReadPermissionsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/rbac/permissions/",
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Permission
   * Create new permission.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns PermissionPublic Successful Response
   * @throws ApiError
   */
  public static createPermission(
    data: RbacCreatePermissionData,
  ): CancelablePromise<RbacCreatePermissionResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/rbac/permissions/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Assign User Role
   * Assign role to user.
   * @param data The data for the request.
   * @param data.userId
   * @param data.requestBody
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static assignUserRole(
    data: RbacAssignUserRoleData,
  ): CancelablePromise<RbacAssignUserRoleResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/rbac/users/{user_id}/roles",
      path: {
        user_id: data.userId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get User Roles Endpoint
   * Get user's roles.
   * @param data The data for the request.
   * @param data.userId
   * @returns RolesPublic Successful Response
   * @throws ApiError
   */
  public static getUserRolesEndpoint(
    data: RbacGetUserRolesEndpointData,
  ): CancelablePromise<RbacGetUserRolesEndpointResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/rbac/users/{user_id}/roles",
      path: {
        user_id: data.userId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Remove User Role
   * Remove role from user.
   * @param data The data for the request.
   * @param data.userId
   * @param data.roleId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static removeUserRole(
    data: RbacRemoveUserRoleData,
  ): CancelablePromise<RbacRemoveUserRoleResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/rbac/users/{user_id}/roles/{role_id}",
      path: {
        user_id: data.userId,
        role_id: data.roleId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get User Permissions Endpoint
   * Get user's permissions.
   * @param data The data for the request.
   * @param data.userId
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static getUserPermissionsEndpoint(
    data: RbacGetUserPermissionsEndpointData,
  ): CancelablePromise<RbacGetUserPermissionsEndpointResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/rbac/users/{user_id}/permissions",
      path: {
        user_id: data.userId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Assign Role Permission
   * Assign permission to role.
   * @param data The data for the request.
   * @param data.roleId
   * @param data.requestBody
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static assignRolePermission(
    data: RbacAssignRolePermissionData,
  ): CancelablePromise<RbacAssignRolePermissionResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/rbac/roles/{role_id}/permissions",
      path: {
        role_id: data.roleId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Role Permissions Endpoint
   * Get role's permissions.
   * @param data The data for the request.
   * @param data.roleId
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static getRolePermissionsEndpoint(
    data: RbacGetRolePermissionsEndpointData,
  ): CancelablePromise<RbacGetRolePermissionsEndpointResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/rbac/roles/{role_id}/permissions",
      path: {
        role_id: data.roleId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Remove Role Permission
   * Remove permission from role.
   * @param data The data for the request.
   * @param data.roleId
   * @param data.permissionId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static removeRolePermission(
    data: RbacRemoveRolePermissionData,
  ): CancelablePromise<RbacRemoveRolePermissionResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/rbac/roles/{role_id}/permissions/{permission_id}",
      path: {
        role_id: data.roleId,
        permission_id: data.permissionId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Initialize Rbac
   * Initialize default roles and permissions.
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static initializeRbac(): CancelablePromise<RbacInitializeRbacResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/rbac/initialize",
    })
  }
}

export class SchoolsService {
  /**
   * Read Schools
   * Retrieve schools.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @param data.includeDeleted
   * @returns SchoolsPublic Successful Response
   * @throws ApiError
   */
  public static readSchools(
    data: SchoolsReadSchoolsData = {},
  ): CancelablePromise<SchoolsReadSchoolsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/schools/",
      query: {
        skip: data.skip,
        limit: data.limit,
        include_deleted: data.includeDeleted,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create School
   * Create new school.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns SchoolPublic Successful Response
   * @throws ApiError
   */
  public static createSchool(
    data: SchoolsCreateSchoolData,
  ): CancelablePromise<SchoolsCreateSchoolResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/schools/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read School
   * Get school by ID.
   * @param data The data for the request.
   * @param data.schoolId
   * @returns SchoolPublic Successful Response
   * @throws ApiError
   */
  public static readSchool(
    data: SchoolsReadSchoolData,
  ): CancelablePromise<SchoolsReadSchoolResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/schools/{school_id}",
      path: {
        school_id: data.schoolId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update School
   * Update a school.
   * @param data The data for the request.
   * @param data.schoolId
   * @param data.requestBody
   * @returns SchoolPublic Successful Response
   * @throws ApiError
   */
  public static updateSchool(
    data: SchoolsUpdateSchoolData,
  ): CancelablePromise<SchoolsUpdateSchoolResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/schools/{school_id}",
      path: {
        school_id: data.schoolId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete School
   * Soft delete a school.
   * @param data The data for the request.
   * @param data.schoolId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteSchool(
    data: SchoolsDeleteSchoolData,
  ): CancelablePromise<SchoolsDeleteSchoolResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/schools/{school_id}",
      path: {
        school_id: data.schoolId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class ThreatDetectionService {
  /**
   * Read Threat Detections
   * Retrieve threat detections.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @param data.activeOnly
   * @returns ThreatDetectionsPublic Successful Response
   * @throws ApiError
   */
  public static readThreatDetections(
    data: ThreatDetectionReadThreatDetectionsData = {},
  ): CancelablePromise<ThreatDetectionReadThreatDetectionsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/threat-detection/",
      query: {
        skip: data.skip,
        limit: data.limit,
        active_only: data.activeOnly,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Threat Detection
   * Get threat detection by ID.
   * @param data The data for the request.
   * @param data.threatId
   * @returns ThreatDetectionPublic Successful Response
   * @throws ApiError
   */
  public static readThreatDetection(
    data: ThreatDetectionReadThreatDetectionData,
  ): CancelablePromise<ThreatDetectionReadThreatDetectionResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/threat-detection/{threat_id}",
      path: {
        threat_id: data.threatId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Threat Detection
   * Update a threat detection (e.g., mark as resolved).
   * @param data The data for the request.
   * @param data.threatId
   * @param data.requestBody
   * @returns ThreatDetectionPublic Successful Response
   * @throws ApiError
   */
  public static updateThreatDetection(
    data: ThreatDetectionUpdateThreatDetectionData,
  ): CancelablePromise<ThreatDetectionUpdateThreatDetectionResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/threat-detection/{threat_id}",
      path: {
        threat_id: data.threatId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Threat Detection
   * Delete a threat detection.
   * @param data The data for the request.
   * @param data.threatId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteThreatDetection(
    data: ThreatDetectionDeleteThreatDetectionData,
  ): CancelablePromise<ThreatDetectionDeleteThreatDetectionResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/threat-detection/{threat_id}",
      path: {
        threat_id: data.threatId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Threat Detection Interface
   * Serve the threat detection web interface.
   * @returns string Successful Response
   * @throws ApiError
   */
  public static getThreatDetectionInterface(): CancelablePromise<ThreatDetectionGetThreatDetectionInterfaceResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/threat-detection/interface/",
    })
  }
}

export class UsersService {
  /**
   * Read Users
   * Retrieve users.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @returns UsersPublic Successful Response
   * @throws ApiError
   */
  public static readUsers(
    data: UsersReadUsersData = {},
  ): CancelablePromise<UsersReadUsersResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/users/",
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create User
   * Create new user.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static createUser(
    data: UsersCreateUserData,
  ): CancelablePromise<UsersCreateUserResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/users/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read User Me
   * Get current user.
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static readUserMe(): CancelablePromise<UsersReadUserMeResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/users/me",
    })
  }

  /**
   * Delete User Me
   * Delete own user.
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteUserMe(): CancelablePromise<UsersDeleteUserMeResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/users/me",
    })
  }

  /**
   * Update User Me
   * Update own user.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static updateUserMe(
    data: UsersUpdateUserMeData,
  ): CancelablePromise<UsersUpdateUserMeResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/users/me",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Password Me
   * Update own password.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static updatePasswordMe(
    data: UsersUpdatePasswordMeData,
  ): CancelablePromise<UsersUpdatePasswordMeResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/users/me/password",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Register User
   * Create new user without the need to be logged in.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static registerUser(
    data: UsersRegisterUserData,
  ): CancelablePromise<UsersRegisterUserResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/users/signup",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read User By Id
   * Get a specific user by id.
   * @param data The data for the request.
   * @param data.userId
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static readUserById(
    data: UsersReadUserByIdData,
  ): CancelablePromise<UsersReadUserByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/users/{user_id}",
      path: {
        user_id: data.userId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update User
   * Update a user.
   * @param data The data for the request.
   * @param data.userId
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static updateUser(
    data: UsersUpdateUserData,
  ): CancelablePromise<UsersUpdateUserResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/users/{user_id}",
      path: {
        user_id: data.userId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete User
   * Delete a user.
   * @param data The data for the request.
   * @param data.userId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteUser(
    data: UsersDeleteUserData,
  ): CancelablePromise<UsersDeleteUserResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/users/{user_id}",
      path: {
        user_id: data.userId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class UtilsService {
  /**
   * Test Email
   * Test emails.
   * @param data The data for the request.
   * @param data.emailTo
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static testEmail(
    data: UtilsTestEmailData,
  ): CancelablePromise<UtilsTestEmailResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/utils/test-email/",
      query: {
        email_to: data.emailTo,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Health Check
   * @returns boolean Successful Response
   * @throws ApiError
   */
  public static healthCheck(): CancelablePromise<UtilsHealthCheckResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/utils/health-check/",
    })
  }
}
