// RBAC service for frontend client
// This should match the backend RBAC API endpoints

import { OpenAPI } from "./core/OpenAPI"
import { request as __request } from "./core/request"
import { CancelablePromise } from "./core/CancelablePromise"
import {
  RbacReadRolesData,
  RbacReadRolesResponse,
  RbacCreateRoleData,
  RbacCreateRoleResponse,
  RbacReadRoleData,
  RbacReadRoleResponse,
  RbacUpdateRoleData,
  RbacUpdateRoleResponse,
  RbacDeleteRoleData,
  RbacDeleteRoleResponse,
  RbacReadPermissionsData,
  RbacReadPermissionsResponse,
  RbacCreatePermissionData,
  RbacCreatePermissionResponse,
  RbacAssignUserRoleData,
  RbacAssignUserRoleResponse,
  RbacRemoveUserRoleData,
  RbacRemoveUserRoleResponse,
  RbacGetUserRolesData,
  RbacGetUserRolesResponse,
  RbacGetUserPermissionsData,
  RbacGetUserPermissionsResponse,
  RbacAssignRolePermissionData,
  RbacAssignRolePermissionResponse,
  RbacRemoveRolePermissionData,
  RbacRemoveRolePermissionResponse,
  RbacGetRolePermissionsData,
  RbacGetRolePermissionsResponse,
  RbacInitializeData,
  RbacInitializeResponse,
} from "./types.gen.rbac"

export class RbacService {
  /**
   * Read Roles
   * Retrieve roles.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @returns RolesPublic Successful Response
   * @throws ApiError
   */
  public static readRoles(
    data: RbacReadRolesData = {},
  ): CancelablePromise<RbacReadRolesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/rbac/roles/",
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Role
   * Create new role.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns RolePublic Successful Response
   * @throws ApiError
   */
  public static createRole(
    data: RbacCreateRoleData,
  ): CancelablePromise<RbacCreateRoleResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/rbac/roles/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Role
   * Get role by ID.
   * @param data The data for the request.
   * @param data.roleId
   * @returns RolePublic Successful Response
   * @throws ApiError
   */
  public static readRole(
    data: RbacReadRoleData,
  ): CancelablePromise<RbacReadRoleResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: `/api/v1/rbac/roles/${data.roleId}`,
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Role
   * Update role.
   * @param data The data for the request.
   * @param data.roleId
   * @param data.requestBody
   * @returns RolePublic Successful Response
   * @throws ApiError
   */
  public static updateRole(
    data: RbacUpdateRoleData,
  ): CancelablePromise<RbacUpdateRoleResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: `/api/v1/rbac/roles/${data.roleId}`,
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Role
   * Delete role.
   * @param data The data for the request.
   * @param data.roleId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteRole(
    data: RbacDeleteRoleData,
  ): CancelablePromise<RbacDeleteRoleResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: `/api/v1/rbac/roles/${data.roleId}`,
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Permissions
   * Retrieve permissions.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @returns PermissionsPublic Successful Response
   * @throws ApiError
   */
  public static readPermissions(
    data: RbacReadPermissionsData = {},
  ): CancelablePromise<RbacReadPermissionsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/rbac/permissions/",
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Permission
   * Create new permission.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns PermissionPublic Successful Response
   * @throws ApiError
   */
  public static createPermission(
    data: RbacCreatePermissionData,
  ): CancelablePromise<RbacCreatePermissionResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/rbac/permissions/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Assign Role to User
   * Assign role to user.
   * @param data The data for the request.
   * @param data.userId
   * @param data.requestBody
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static assignUserRole(
    data: RbacAssignUserRoleData,
  ): CancelablePromise<RbacAssignUserRoleResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: `/api/v1/rbac/users/${data.userId}/roles`,
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Remove Role from User
   * Remove role from user.
   * @param data The data for the request.
   * @param data.userId
   * @param data.roleId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static removeUserRole(
    data: RbacRemoveUserRoleData,
  ): CancelablePromise<RbacRemoveUserRoleResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: `/api/v1/rbac/users/${data.userId}/roles/${data.roleId}`,
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get User Roles
   * Get user's roles.
   * @param data The data for the request.
   * @param data.userId
   * @returns RolesPublic Successful Response
   * @throws ApiError
   */
  public static getUserRoles(
    data: RbacGetUserRolesData,
  ): CancelablePromise<RbacGetUserRolesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: `/api/v1/rbac/users/${data.userId}/roles`,
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get User Permissions
   * Get user's permissions.
   * @param data The data for the request.
   * @param data.userId
   * @returns Permissions Successful Response
   * @throws ApiError
   */
  public static getUserPermissions(
    data: RbacGetUserPermissionsData,
  ): CancelablePromise<RbacGetUserPermissionsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: `/api/v1/rbac/users/${data.userId}/permissions`,
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Assign Permission to Role
   * Assign permission to role.
   * @param data The data for the request.
   * @param data.roleId
   * @param data.requestBody
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static assignRolePermission(
    data: RbacAssignRolePermissionData,
  ): CancelablePromise<RbacAssignRolePermissionResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: `/api/v1/rbac/roles/${data.roleId}/permissions`,
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Remove Permission from Role
   * Remove permission from role.
   * @param data The data for the request.
   * @param data.roleId
   * @param data.permissionId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static removeRolePermission(
    data: RbacRemoveRolePermissionData,
  ): CancelablePromise<RbacRemoveRolePermissionResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: `/api/v1/rbac/roles/${data.roleId}/permissions/${data.permissionId}`,
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Role Permissions
   * Get role's permissions.
   * @param data The data for the request.
   * @param data.roleId
   * @returns Permissions Successful Response
   * @throws ApiError
   */
  public static getRolePermissions(
    data: RbacGetRolePermissionsData,
  ): CancelablePromise<RbacGetRolePermissionsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: `/api/v1/rbac/roles/${data.roleId}/permissions`,
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Initialize RBAC
   * Initialize default roles and permissions.
   * @param data The data for the request.
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static initializeRbac(
    _data: RbacInitializeData = {},
  ): CancelablePromise<RbacInitializeResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/rbac/initialize",
      errors: {
        422: "Validation Error",
      },
    })
  }
}
