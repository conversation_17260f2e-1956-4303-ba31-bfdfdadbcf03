// Manually added types for Schools API
// These types should match the backend models

export type SchoolCreate = {
  name: string
  address?: string | null
  phone?: string | null
  email?: string | null
}

export type SchoolUpdate = {
  name?: string | null
  address?: string | null
  phone?: string | null
  email?: string | null
}

export type SchoolPublic = {
  name: string
  address?: string | null
  phone?: string | null
  email?: string | null
  id: string
  created_at: string
  updated_at?: string | null
}

export type SchoolsPublic = {
  data: Array<SchoolPublic>
  count: number
}

export type SchoolsReadSchoolsData = {
  limit?: number
  skip?: number
}

export type SchoolsReadSchoolsResponse = SchoolsPublic

export type SchoolsCreateSchoolData = {
  requestBody: SchoolCreate
}

export type SchoolsCreateSchoolResponse = SchoolPublic

export type SchoolsReadSchoolData = {
  id: string
}

export type SchoolsReadSchoolResponse = SchoolPublic

export type SchoolsUpdateSchoolData = {
  id: string
  requestBody: SchoolUpdate
}

export type SchoolsUpdateSchoolResponse = SchoolPublic

export type SchoolsDeleteSchoolData = {
  id: string
}

export type SchoolsDeleteSchoolResponse = {
  message: string
}
