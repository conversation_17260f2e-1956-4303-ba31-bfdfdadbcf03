// This file is auto-generated by @hey-api/openapi-ts

export type Body_login_login_access_token = {
  grant_type?: string | null
  username: string
  password: string
  scope?: string
  client_id?: string | null
  client_secret?: string | null
}

export type BuildingCreate = {
  name: string
  school_id: string
  address?: string | null
  description?: string | null
}

export type BuildingPublic = {
  name: string
  school_id: string
  address?: string | null
  description?: string | null
  id: string
  created_at: string
  updated_at?: string | null
}

export type BuildingsPublic = {
  data: Array<BuildingPublic>
  count: number
}

export type BuildingUpdate = {
  name?: string | null
  school_id?: string | null
  address?: string | null
  description?: string | null
}

export type FloorCreate = {
  number: number
  building_id: string
  base_path?: string | null
  file_path?: string | null
}

export type FloorPublic = {
  number: number
  building_id: string
  base_path?: string | null
  file_path?: string | null
  id: string
  created_at: string
  updated_at?: string | null
}

export type FloorsPublic = {
  data: Array<FloorPublic>
  count: number
}

export type FloorUpdate = {
  number?: number | null
  base_path?: string | null
  file_path?: string | null
}

export type HTTPValidationError = {
  detail?: Array<ValidationError>
}

export type ItemCreate = {
  title: string
  description?: string | null
}

export type ItemPublic = {
  title: string
  description?: string | null
  id: string
  owner_id: string
}

export type ItemsPublic = {
  data: Array<ItemPublic>
  count: number
}

export type ItemUpdate = {
  title?: string | null
  description?: string | null
}

export type Message = {
  message: string
}

export type NewPassword = {
  token: string
  new_password: string
}

export type PermissionCreate = {
  name: PermissionType
  description?: string | null
  resource?: string | null
}

export type PermissionPublic = {
  name: PermissionType
  description?: string | null
  resource?: string | null
  id: string
  created_at: string
}

export type PermissionsPublic = {
  data: Array<PermissionPublic>
  count: number
}

export type PermissionType =
  | "create_user"
  | "read_user"
  | "update_user"
  | "delete_user"
  | "create_school"
  | "read_school"
  | "update_school"
  | "delete_school"
  | "view_threats"
  | "manage_threats"
  | "configure_cameras"
  | "system_config"
  | "view_logs"
  | "manage_roles"
  | "manage_classes"
  | "view_students"
  | "manage_curriculum"

export type PrivateUserCreate = {
  email: string
  password: string
  full_name: string
  is_verified?: boolean
}

export type RoleCreate = {
  name: RoleType
  description?: string | null
  is_active?: boolean
}

export type RolePermissionAssign = {
  role_id: string
  permission_id: string
}

export type RolePublic = {
  name: RoleType
  description?: string | null
  is_active?: boolean
  id: string
  created_at: string
  updated_at?: string | null
}

export type RolesPublic = {
  data: Array<RolePublic>
  count: number
}

export type RoleType = "admin" | "teachers" | "management" | "security" | "it"

export type RoleUpdate = {
  description?: string | null
  is_active?: boolean | null
}

export type SchoolCreate = {
  name: string
  address?: string | null
  phone?: string | null
  email?: string | null
}

export type SchoolPublic = {
  name: string
  address?: string | null
  phone?: string | null
  email?: string | null
  id: string
  created_at: string
  updated_at?: string | null
}

export type SchoolsPublic = {
  data: Array<SchoolPublic>
  count: number
}

export type SchoolUpdate = {
  name?: string | null
  address?: string | null
  phone?: string | null
  email?: string | null
}

export type ThreatDetectionPublic = {
  camera_id: string
  threat_type: string
  confidence: number
  bbox_x1: number
  bbox_y1: number
  bbox_x2: number
  bbox_y2: number
  image_path?: string | null
  is_active_threat?: boolean
  id: string
  detected_at: string
  resolved_at?: string | null
}

export type ThreatDetectionsPublic = {
  data: Array<ThreatDetectionPublic>
  count: number
}

export type ThreatDetectionUpdate = {
  is_active_threat?: boolean | null
}

export type Token = {
  access_token: string
  token_type?: string
}

export type UpdatePassword = {
  current_password: string
  new_password: string
}

export type UserCreate = {
  email: string
  is_active?: boolean
  is_superuser?: boolean
  full_name?: string | null
  password: string
}

export type UserPublic = {
  email: string
  is_active?: boolean
  is_superuser?: boolean
  full_name?: string | null
  id: string
  roles?: Array<RolePublic>
}

export type UserRegister = {
  email: string
  password: string
  full_name?: string | null
}

export type UserRoleAssign = {
  user_id: string
  role_id: string
}

export type UsersPublic = {
  data: Array<UserPublic>
  count: number
}

export type UserUpdate = {
  email?: string | null
  is_active?: boolean
  is_superuser?: boolean
  full_name?: string | null
  password?: string | null
}

export type UserUpdateMe = {
  full_name?: string | null
  email?: string | null
}

export type ValidationError = {
  loc: Array<string | number>
  msg: string
  type: string
}

export type BuildingsReadBuildingsData = {
  includeDeleted?: boolean
  limit?: number
  schoolId?: string | null
  skip?: number
}

export type BuildingsReadBuildingsResponse = BuildingsPublic

export type BuildingsCreateBuildingData = {
  requestBody: BuildingCreate
}

export type BuildingsCreateBuildingResponse = BuildingPublic

export type BuildingsReadBuildingData = {
  buildingId: string
}

export type BuildingsReadBuildingResponse = BuildingPublic

export type BuildingsUpdateBuildingData = {
  buildingId: string
  requestBody: BuildingUpdate
}

export type BuildingsUpdateBuildingResponse = BuildingPublic

export type BuildingsDeleteBuildingData = {
  buildingId: string
}

export type BuildingsDeleteBuildingResponse = Message

export type FloorsReadFloorsData = {
  buildingId?: string | null
  includeDeleted?: boolean
  limit?: number
  skip?: number
}

export type FloorsReadFloorsResponse = FloorsPublic

export type FloorsCreateFloorData = {
  requestBody: FloorCreate
}

export type FloorsCreateFloorResponse = FloorPublic

export type FloorsReadFloorData = {
  floorId: string
}

export type FloorsReadFloorResponse = FloorPublic

export type FloorsUpdateFloorData = {
  floorId: string
  requestBody: FloorUpdate
}

export type FloorsUpdateFloorResponse = FloorPublic

export type FloorsDeleteFloorData = {
  floorId: string
}

export type FloorsDeleteFloorResponse = Message

export type FloorsUploadFloorPlanData = {
  floorId: string
  formData: {
    file: File
  }
}

export type FloorsUploadFloorPlanResponse = FloorPublic

export type ItemsReadItemsData = {
  limit?: number
  skip?: number
}

export type ItemsReadItemsResponse = ItemsPublic

export type ItemsCreateItemData = {
  requestBody: ItemCreate
}

export type ItemsCreateItemResponse = ItemPublic

export type ItemsReadItemData = {
  id: string
}

export type ItemsReadItemResponse = ItemPublic

export type ItemsUpdateItemData = {
  id: string
  requestBody: ItemUpdate
}

export type ItemsUpdateItemResponse = ItemPublic

export type ItemsDeleteItemData = {
  id: string
}

export type ItemsDeleteItemResponse = Message

export type LoginLoginAccessTokenData = {
  formData: Body_login_login_access_token
}

export type LoginLoginAccessTokenResponse = Token

export type LoginTestTokenResponse = UserPublic

export type LoginRecoverPasswordData = {
  email: string
}

export type LoginRecoverPasswordResponse = Message

export type LoginResetPasswordData = {
  requestBody: NewPassword
}

export type LoginResetPasswordResponse = Message

export type LoginRecoverPasswordHtmlContentData = {
  email: string
}

export type LoginRecoverPasswordHtmlContentResponse = string

export type PrivateCreateUserData = {
  requestBody: PrivateUserCreate
}

export type PrivateCreateUserResponse = UserPublic

export type RbacReadRolesData = {
  limit?: number
  skip?: number
}

export type RbacReadRolesResponse = RolesPublic

export type RbacCreateRoleData = {
  requestBody: RoleCreate
}

export type RbacCreateRoleResponse = RolePublic

export type RbacReadRoleData = {
  roleId: string
}

export type RbacReadRoleResponse = RolePublic

export type RbacUpdateRoleData = {
  requestBody: RoleUpdate
  roleId: string
}

export type RbacUpdateRoleResponse = RolePublic

export type RbacDeleteRoleData = {
  roleId: string
}

export type RbacDeleteRoleResponse = Message

export type RbacReadPermissionsData = {
  limit?: number
  skip?: number
}

export type RbacReadPermissionsResponse = PermissionsPublic

export type RbacCreatePermissionData = {
  requestBody: PermissionCreate
}

export type RbacCreatePermissionResponse = PermissionPublic

export type RbacAssignUserRoleData = {
  requestBody: UserRoleAssign
  userId: string
}

export type RbacAssignUserRoleResponse = Message

export type RbacGetUserRolesEndpointData = {
  userId: string
}

export type RbacGetUserRolesEndpointResponse = RolesPublic

export type RbacRemoveUserRoleData = {
  roleId: string
  userId: string
}

export type RbacRemoveUserRoleResponse = Message

export type RbacGetUserPermissionsEndpointData = {
  userId: string
}

export type RbacGetUserPermissionsEndpointResponse = unknown

export type RbacAssignRolePermissionData = {
  requestBody: RolePermissionAssign
  roleId: string
}

export type RbacAssignRolePermissionResponse = Message

export type RbacGetRolePermissionsEndpointData = {
  roleId: string
}

export type RbacGetRolePermissionsEndpointResponse = unknown

export type RbacRemoveRolePermissionData = {
  permissionId: string
  roleId: string
}

export type RbacRemoveRolePermissionResponse = Message

export type RbacInitializeRbacResponse = Message

export type SchoolsReadSchoolsData = {
  includeDeleted?: boolean
  limit?: number
  skip?: number
}

export type SchoolsReadSchoolsResponse = SchoolsPublic

export type SchoolsCreateSchoolData = {
  requestBody: SchoolCreate
}

export type SchoolsCreateSchoolResponse = SchoolPublic

export type SchoolsReadSchoolData = {
  schoolId: string
}

export type SchoolsReadSchoolResponse = SchoolPublic

export type SchoolsUpdateSchoolData = {
  requestBody: SchoolUpdate
  schoolId: string
}

export type SchoolsUpdateSchoolResponse = SchoolPublic

export type SchoolsDeleteSchoolData = {
  schoolId: string
}

export type SchoolsDeleteSchoolResponse = Message

export type ThreatDetectionReadThreatDetectionsData = {
  activeOnly?: boolean
  limit?: number
  skip?: number
}

export type ThreatDetectionReadThreatDetectionsResponse = ThreatDetectionsPublic

export type ThreatDetectionReadThreatDetectionData = {
  threatId: string
}

export type ThreatDetectionReadThreatDetectionResponse = ThreatDetectionPublic

export type ThreatDetectionUpdateThreatDetectionData = {
  requestBody: ThreatDetectionUpdate
  threatId: string
}

export type ThreatDetectionUpdateThreatDetectionResponse = ThreatDetectionPublic

export type ThreatDetectionDeleteThreatDetectionData = {
  threatId: string
}

export type ThreatDetectionDeleteThreatDetectionResponse = Message

export type ThreatDetectionGetThreatDetectionInterfaceResponse = string

export type UsersReadUsersData = {
  limit?: number
  skip?: number
}

export type UsersReadUsersResponse = UsersPublic

export type UsersCreateUserData = {
  requestBody: UserCreate
}

export type UsersCreateUserResponse = UserPublic

export type UsersReadUserMeResponse = UserPublic

export type UsersDeleteUserMeResponse = Message

export type UsersUpdateUserMeData = {
  requestBody: UserUpdateMe
}

export type UsersUpdateUserMeResponse = UserPublic

export type UsersUpdatePasswordMeData = {
  requestBody: UpdatePassword
}

export type UsersUpdatePasswordMeResponse = Message

export type UsersRegisterUserData = {
  requestBody: UserRegister
}

export type UsersRegisterUserResponse = UserPublic

export type UsersReadUserByIdData = {
  userId: string
}

export type UsersReadUserByIdResponse = UserPublic

export type UsersUpdateUserData = {
  requestBody: UserUpdate
  userId: string
}

export type UsersUpdateUserResponse = UserPublic

export type UsersDeleteUserData = {
  userId: string
}

export type UsersDeleteUserResponse = Message

export type UtilsTestEmailData = {
  emailTo: string
}

export type UtilsTestEmailResponse = Message

export type UtilsHealthCheckResponse = boolean
