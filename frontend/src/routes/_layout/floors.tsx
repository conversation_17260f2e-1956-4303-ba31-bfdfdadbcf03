import { useQuery } from "@tanstack/react-query"
import { createFileRoute } from "@tanstack/react-router"
import { z } from "zod"

import { Box, Container, Heading, Text } from "@chakra-ui/react"

import { FloorsService } from "@/client"
import AddFloor from "@/components/Floors/AddFloor"
import FloorsTable from "@/components/Floors/FloorsTable"
import Navbar from "@/components/Common/Navbar"

const floorsSearchSchema = z.object({
  buildingId: z.string().optional(),
})

export const Route = createFileRoute("/_layout/floors")({
  component: Floors,
  validateSearch: floorsSearchSchema,
})

function Floors() {
  const { buildingId } = Route.useSearch()
  
  const { data: floors, isLoading } = useQuery({
    queryKey: ["floors", { buildingId }],
    queryFn: () =>
      FloorsService.readFloors({
        buildingId: buildingId || undefined,
      }),
  })

  return (
    <>
      <Navbar />
      <Container maxW="full">
        <Heading size="lg" textAlign={{ base: "center", md: "left" }} pt={12}>
          Floors Management
        </Heading>

        {buildingId && <AddFloor buildingId={buildingId} />}
        
        {!buildingId && (
          <Box my={4}>
            <Text color="gray.600">
              Please select a building from the Buildings page to manage its floors.
            </Text>
          </Box>
        )}

        {floors?.data && floors.data.length > 0 ? (
          <FloorsTable floors={floors.data} isLoading={isLoading} />
        ) : (
          !isLoading && buildingId && (
            <Box my={4}>
              <Text>No floors found for this building.</Text>
            </Box>
          )
        )}
      </Container>
    </>
  )
}
