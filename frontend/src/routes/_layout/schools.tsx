import {
  But<PERSON>,
  Container,
  EmptyState,
  Flex,
  Heading,
  Table,
  Text,
  VStack,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { FiSearch } from "react-icons/fi"
import { z } from "zod"

import { SchoolsService } from "@/client"
import { SchoolActionsMenu } from "@/components/Common/SchoolActionsMenu"
import PendingSchools from "@/components/Pending/PendingSchools"
import AddSchool from "@/components/Schools/AddSchool"
import {
  PaginationItems,
  PaginationNextTrigger,
  PaginationPrevTrigger,
  PaginationRoot,
} from "@/components/ui/pagination.tsx"

const schoolsSearchSchema = z.object({
  page: z.number().catch(1),
})

const PER_PAGE = 5

function getSchoolsQueryOptions({ page }: { page: number }) {
  return {
    queryFn: () =>
      SchoolsService.readSchools({ skip: (page - 1) * PER_PAGE, limit: PER_PAGE }),
    queryKey: ["schools", { page }],
  }
}

export const Route = createFileRoute("/_layout/schools")({
  component: Schools,
  validateSearch: (search) => schoolsSearchSchema.parse(search),
})

function Schools() {
  const { page } = Route.useSearch()
  const navigate = useNavigate()
  const { data, isLoading, isError } = useQuery(getSchoolsQueryOptions({ page }))

  const handlePageChange = (details: { page: number }) => {
    navigate({
      search: {
        page: details.page,
      },
    })
  }

  return (
    <Container maxW="full">
      <Flex justifyContent="space-between" alignItems="center">
        <Heading size="md">Schools</Heading>
        <AddSchool />
      </Flex>

      {isLoading ? (
        <PendingSchools />
      ) : isError ? (
        <Text>Error loading schools.</Text>
      ) : data?.data.length === 0 ? (
        <EmptyState.Root>
          <EmptyState.Content>
            <EmptyState.Indicator>
              <FiSearch />
            </EmptyState.Indicator>
            <VStack textAlign="center">
              <EmptyState.Title>No schools found</EmptyState.Title>
              <EmptyState.Description>
                There are no schools in the system yet.
              </EmptyState.Description>
            </VStack>
          </EmptyState.Content>
        </EmptyState.Root>
      ) : (
        <VStack align="stretch" gap={4}>
          <Table.Root size={{ base: "sm", md: "md" }}>
            <Table.Header>
              <Table.Row>
                <Table.ColumnHeader w="sm">Name</Table.ColumnHeader>
                <Table.ColumnHeader w="sm">Address</Table.ColumnHeader>
                <Table.ColumnHeader w="sm">Phone</Table.ColumnHeader>
                <Table.ColumnHeader w="sm">Email</Table.ColumnHeader>
                <Table.ColumnHeader w="sm">Actions</Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {data?.data.map((school) => (
                <Table.Row key={school.id}>
                  <Table.Cell>{school.name}</Table.Cell>
                  <Table.Cell>{school.address || "-"}</Table.Cell>
                  <Table.Cell>{school.phone || "-"}</Table.Cell>
                  <Table.Cell>{school.email || "-"}</Table.Cell>
                  <Table.Cell>
                    <SchoolActionsMenu school={school} />
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table.Root>

          {data && data.count > PER_PAGE && (
            <PaginationRoot
              count={data.count}
              pageSize={PER_PAGE}
              siblingCount={1}
              page={page}
              onPageChange={handlePageChange}
            >
              <Flex justify="space-between" align="center">
                <PaginationPrevTrigger asChild>
                  <Button variant="outline">Previous</Button>
                </PaginationPrevTrigger>
                <PaginationItems />
                <PaginationNextTrigger asChild>
                  <Button variant="outline">Next</Button>
                </PaginationNextTrigger>
              </Flex>
            </PaginationRoot>
          )}
        </VStack>
      )}
    </Container>
  )
}
