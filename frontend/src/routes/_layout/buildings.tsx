import { useQuery } from "@tanstack/react-query"
import { createFileRoute } from "@tanstack/react-router"
import { z } from "zod"

import { Box, Container, Heading, Text } from "@chakra-ui/react"

import { BuildingsService } from "@/client"
import AddBuilding from "@/components/Buildings/AddBuilding"
import BuildingsTable from "@/components/Buildings/BuildingsTable"
import Navbar from "@/components/Common/Navbar"

const buildingsSearchSchema = z.object({
  schoolId: z.string().optional(),
})

export const Route = createFileRoute("/_layout/buildings")({
  component: Buildings,
  validateSearch: buildingsSearchSchema,
})

function Buildings() {
  const { schoolId } = Route.useSearch()
  
  const { data: buildings, isLoading } = useQuery({
    queryKey: ["buildings", { schoolId }],
    queryFn: () =>
      BuildingsService.readBuildings({
        schoolId: schoolId || undefined,
      }),
  })

  return (
    <>
      <Navbar />
      <Container maxW="full">
        <Heading size="lg" textAlign={{ base: "center", md: "left" }} pt={12}>
          Buildings Management
        </Heading>

        {schoolId && <AddBuilding schoolId={schoolId} />}
        
        {!schoolId && (
          <Box my={4}>
            <Text color="gray.600">
              Please select a school from the Schools page to manage its buildings.
            </Text>
          </Box>
        )}

        {buildings?.data && buildings.data.length > 0 ? (
          <BuildingsTable buildings={buildings.data} isLoading={isLoading} />
        ) : (
          !isLoading && schoolId && (
            <Box my={4}>
              <Text>No buildings found for this school.</Text>
            </Box>
          )
        )}
      </Container>
    </>
  )
}
