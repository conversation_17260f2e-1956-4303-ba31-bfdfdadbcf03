import { Container, Heading, Tabs } from "@chakra-ui/react"
import { createFileRoute } from "@tanstack/react-router"

import RolesManagement from "@/components/RBAC/RolesManagement"
import PermissionsManagement from "@/components/RBAC/PermissionsManagement"
import UserRolesManagement from "@/components/RBAC/UserRolesManagement"

export const Route = createFileRoute("/_layout/rbac")({
  component: RBAC,
})

function RBAC() {
  return (
    <Container maxW="full">
      <Heading size="lg" pt={12} mb={6}>
        Role-Based Access Control (RBAC)
      </Heading>

      <Tabs.Root defaultValue="roles" variant="enclosed">
        <Tabs.List>
          <Tabs.Trigger value="roles">Roles Management</Tabs.Trigger>
          <Tabs.Trigger value="permissions">Permissions Management</Tabs.Trigger>
          <Tabs.Trigger value="user-roles">User Roles Assignment</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="roles" pt={4}>
          <RolesManagement />
        </Tabs.Content>

        <Tabs.Content value="permissions" pt={4}>
          <PermissionsManagement />
        </Tabs.Content>

        <Tabs.Content value="user-roles" pt={4}>
          <UserRolesManagement />
        </Tabs.Content>
      </Tabs.Root>
    </Container>
  )
}
