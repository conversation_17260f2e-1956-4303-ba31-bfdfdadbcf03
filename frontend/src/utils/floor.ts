import type { FloorPublic } from "@/client"

/**
 * Constructs the full image URL from base_path and file_path
 * @param floor - The floor object containing base_path and file_path
 * @returns The full URL or null if either path is missing
 */
export function getFloorPlanImageUrl(floor: FloorPublic): string | null {
  if (!floor.base_path || !floor.file_path) {
    return null
  }
  
  const basePath = floor.base_path.replace(/\/$/, '') // Remove trailing slash
  const filePath = floor.file_path.replace(/^\//, '') // Remove leading slash
  
  return `${basePath}/${filePath}`
}

/**
 * Checks if a floor has a valid floor plan image
 * @param floor - The floor object to check
 * @returns True if the floor has both base_path and file_path
 */
export function hasFloorPlanImage(floor: FloorPublic): boolean {
  return !!(floor.base_path && floor.file_path)
}
