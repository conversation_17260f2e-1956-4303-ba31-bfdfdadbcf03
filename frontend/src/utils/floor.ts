import type { FloorPublic } from "@/client"

/**
 * Constructs the full image URL from base_path and file_path
 * Uses IMAGE_BASE_URL as prefix for local storage, or uses S3 URLs directly
 * @param floor - The floor object containing base_path and file_path
 * @returns The full URL or null if either path is missing
 */
export function getFloorPlanImageUrl(floor: FloorPublic): string | null {
  if (!floor.base_path || !floor.file_path) {
    return null
  }

  const basePath = floor.base_path.replace(/\/$/, '') // Remove trailing slash
  const filePath = floor.file_path.replace(/^\//, '') // Remove leading slash

  // Check if base_path is already a full URL (S3 case)
  if (basePath.startsWith('http://') || basePath.startsWith('https://')) {
    return `${basePath}/${filePath}`
  }

  // For local storage, use IMAGE_BASE_URL as prefix
  const imageBaseUrl = import.meta.env.VITE_IMAGE_BASE_URL || ''
  if (imageBaseUrl) {
    const cleanBaseUrl = imageBaseUrl.replace(/\/$/, '') // Remove trailing slash
    return `${cleanBaseUrl}${basePath}/${filePath}`
  }

  // Fallback to original behavior
  return `${basePath}/${filePath}`
}

/**
 * Checks if a floor has a valid floor plan image
 * @param floor - The floor object to check
 * @returns True if the floor has both base_path and file_path
 */
export function hasFloorPlanImage(floor: FloorPublic): boolean {
  return !!(floor.base_path && floor.file_path)
}
