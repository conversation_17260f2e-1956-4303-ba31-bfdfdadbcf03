import { useMutation, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"

import {
  Button,
  DialogActionTrigger,
  DialogTitle,
  Input,
  Text,
  VStack,
} from "@chakra-ui/react"
import { useEffect, useState } from "react"

import {
  type FloorPublic,
  type FloorUpdate,
  FloorsService,
} from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import { getFloorPlanImageUrl } from "@/utils/floor"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
} from "../ui/dialog"
import { Field } from "../ui/field"
import FileUpload from "../Common/FileUpload"

interface EditFloorProps {
  floor: FloorPublic
  isOpen: boolean
  onClose: () => void
}

const EditFloor = ({ floor, isOpen, onClose }: EditFloorProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()
  const {
    register,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors, isDirty },
  } = useForm<FloorUpdate>({
    mode: "onBlur",
    criteriaMode: "all",
    defaultValues: floor,
  })

  useEffect(() => {
    reset(floor)
  }, [floor, reset])

  const mutation = useMutation({
    mutationFn: async (data: FloorUpdate) => {
      // First update the floor
      const updatedFloor = await FloorsService.updateFloor({
        floorId: floor.id,
        requestBody: data,
      })

      // Then upload the file if one is selected
      if (selectedFile) {
        const formData = { file: selectedFile }
        await FloorsService.uploadFloorPlan({
          floorId: floor.id,
          formData,
        })
      }

      return updatedFloor
    },
    onSuccess: () => {
      showSuccessToast("Floor updated successfully.")
      setSelectedFile(null)
      onClose()
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["floors"] })
    },
  })

  const onSubmit: SubmitHandler<FloorUpdate> = async (data) => {
    mutation.mutate(data)
  }

  const onCancel = () => {
    reset()
    onClose()
  }

  return (
    <DialogRoot
      size={{ base: "xs", md: "md" }}
      placement="center"
      open={isOpen}
      onOpenChange={({ open }) => !open && onClose()}
    >
      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Edit Floor</DialogTitle>
          </DialogHeader>

          <DialogBody>
            <Text mb={4}>Update the floor details.</Text>
            <VStack gap={4}>
              <Field
                required
                invalid={!!errors.number}
                errorText={errors.number?.message}
                label="Floor Number"
              >
                <Input
                  id="number"
                  {...register("number", {
                    required: "Floor number is required",
                    valueAsNumber: true,
                    min: {
                      value: 0,
                      message: "Floor number must be 0 or greater",
                    },
                  })}
                  placeholder="0"
                  type="number"
                />
              </Field>

              <FileUpload
                onFileSelect={setSelectedFile}
                label="Floor Plan Image"
                accept="image/*"
                maxSize={10}
                disabled={isSubmitting}
                currentImageUrl={getFloorPlanImageUrl(floor)}
              />
            </VStack>
          </DialogBody>

          <DialogFooter gap={2}>
            <DialogActionTrigger asChild>
              <Button
                variant="subtle"
                colorPalette="gray"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </DialogActionTrigger>
            <Button
              variant="solid"
              type="submit"
              disabled={isSubmitting || !isDirty}
              loading={isSubmitting}
            >
              Save
            </Button>
          </DialogFooter>
        </form>
        <DialogCloseTrigger />
      </DialogContent>
    </DialogRoot>
  )
}

export default EditFloor
