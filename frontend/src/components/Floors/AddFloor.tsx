import { useMutation, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"

import {
  Button,
  DialogActionTrigger,
  DialogTitle,
  Input,
  Text,
  VStack,
} from "@chakra-ui/react"
import { useState } from "react"
import { FaPlus } from "react-icons/fa"

import { type FloorCreate, FloorsService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
  DialogTrigger,
} from "../ui/dialog"
import { Field } from "../ui/field"
import FileUpload from "../Common/FileUpload"

interface AddFloorProps {
  buildingId: string
}

const AddFloor = ({ buildingId }: AddFloorProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid, isSubmitting },
  } = useForm<FloorCreate>({
    mode: "onBlur",
    criteriaMode: "all",
    defaultValues: {
      number: 0,
      building_id: buildingId,
      base_path: "",
      file_path: "",
    },
  })

  const mutation = useMutation({
    mutationFn: async (data: FloorCreate) => {
      // First create the floor
      const floor = await FloorsService.createFloor({ requestBody: data })

      // Then upload the file if one is selected
      if (selectedFile) {
        const formData = { file: selectedFile }
        await FloorsService.uploadFloorPlan({
          floorId: floor.id,
          formData,
        })
      }

      return floor
    },
    onSuccess: () => {
      showSuccessToast("Floor created successfully.")
      reset()
      setSelectedFile(null)
      setIsOpen(false)
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["floors"] })
    },
  })

  const onSubmit: SubmitHandler<FloorCreate> = (data) => {
    mutation.mutate(data)
  }

  return (
    <DialogRoot
      size={{ base: "xs", md: "md" }}
      placement="center"
      open={isOpen}
      onOpenChange={({ open }) => setIsOpen(open)}
    >
      <DialogTrigger asChild>
        <Button value="add-floor" my={4}>
          <FaPlus fontSize="16px" />
          Add Floor
        </Button>
      </DialogTrigger>
      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Add Floor</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <Text mb={4}>Fill in the details to add a new floor.</Text>
            <VStack gap={4}>
              <Field
                required
                invalid={!!errors.number}
                errorText={errors.number?.message}
                label="Floor Number"
              >
                <Input
                  id="number"
                  {...register("number", {
                    required: "Floor number is required.",
                    valueAsNumber: true,
                    min: {
                      value: 0,
                      message: "Floor number must be 0 or greater",
                    },
                  })}
                  placeholder="0"
                  type="number"
                />
              </Field>

              <FileUpload
                onFileSelect={setSelectedFile}
                label="Floor Plan Image"
                accept="image/*"
                maxSize={10}
                disabled={isSubmitting}
              />
            </VStack>
          </DialogBody>

          <DialogFooter gap={2}>
            <DialogActionTrigger asChild>
              <Button
                variant="subtle"
                colorPalette="gray"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </DialogActionTrigger>
            <Button
              variant="solid"
              type="submit"
              disabled={!isValid}
              loading={isSubmitting}
            >
              Save
            </Button>
          </DialogFooter>
        </form>
        <DialogCloseTrigger />
      </DialogContent>
    </DialogRoot>
  )
}

export default AddFloor
