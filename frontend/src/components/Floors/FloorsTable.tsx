import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useState } from "react"
import { FaEdit, FaTrash } from "react-icons/fa"

import {
  Container,
  IconButton,
  Skeleton,
  Table,
  Image,
  Box,
} from "@chakra-ui/react"

import { type FloorPublic, FloorsService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import ActionsMenu from "@/components/Common/ActionsMenu"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import { getFloorPlanImageUrl, hasFloorPlanImage } from "@/utils/floor"

import EditFloor from "./EditFloor"

interface FloorsTableProps {
  floors: FloorPublic[]
  isLoading: boolean
}

const FloorsTable = ({ floors, isLoading }: FloorsTableProps) => {
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()
  const [editingFloor, setEditingFloor] = useState<FloorPublic | null>(null)

  const deleteMutation = useMutation({
    mutationFn: (id: string) =>
      FloorsService.deleteFloor({ floorId: id }),
    onSuccess: () => {
      showSuccessToast("Floor deleted successfully.")
      queryClient.invalidateQueries({ queryKey: ["floors"] })
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
  })

  const handleDelete = (id: string) => {
    deleteMutation.mutate(id)
  }

  if (isLoading) {
    return (
      <Container maxW="full">
        <Skeleton height="300px" />
      </Container>
    )
  }

  return (
    <>
      <Table.Root size="md">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader>Floor Number</Table.ColumnHeader>
            <Table.ColumnHeader>Floor Plan Preview</Table.ColumnHeader>
            <Table.ColumnHeader>Floor Plan Link</Table.ColumnHeader>
            <Table.ColumnHeader>Actions</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {floors.map((floor) => (
            <Table.Row key={floor.id}>
              <Table.Cell>Floor {floor.number}</Table.Cell>
              <Table.Cell>
                {hasFloorPlanImage(floor) ? (
                  <Box maxW="100px" maxH="60px" overflow="hidden" borderRadius="md">
                    <Image
                      src={getFloorPlanImageUrl(floor)!}
                      alt={`Floor ${floor.number} plan`}
                      objectFit="cover"
                      w="100%"
                      h="60px"
                      cursor="pointer"
                      onClick={() => window.open(getFloorPlanImageUrl(floor)!, '_blank')}
                      _hover={{ opacity: 0.8 }}
                      transition="opacity 0.2s"
                    />
                  </Box>
                ) : (
                  "-"
                )}
              </Table.Cell>
              <Table.Cell>
                {hasFloorPlanImage(floor) ? (
                  <a
                    href={getFloorPlanImageUrl(floor)!}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ color: "#3182ce", textDecoration: "underline" }}
                  >
                    View Full Size
                  </a>
                ) : (
                  "-"
                )}
              </Table.Cell>
              <Table.Cell>
                <ActionsMenu type="Floor" value={floor}>
                  <IconButton
                    variant="ghost"
                    colorPalette="teal"
                    aria-label="Edit floor"
                    size="sm"
                    onClick={() => setEditingFloor(floor)}
                  >
                    <FaEdit />
                  </IconButton>
                  <IconButton
                    variant="ghost"
                    colorPalette="red"
                    aria-label="Delete floor"
                    size="sm"
                    onClick={() => handleDelete(floor.id)}
                    loading={deleteMutation.isPending}
                  >
                    <FaTrash />
                  </IconButton>
                </ActionsMenu>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>

      {editingFloor && (
        <EditFloor
          floor={editingFloor}
          isOpen={!!editingFloor}
          onClose={() => setEditingFloor(null)}
        />
      )}
    </>
  )
}

export default FloorsTable
