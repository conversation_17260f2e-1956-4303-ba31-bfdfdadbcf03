import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useNavigate } from "@tanstack/react-router"
import { useState } from "react"
import { FaEdit, FaTrash, FaLayerGroup } from "react-icons/fa"

import {
  Box,
  Button,
  Container,
  Flex,
  Heading,
  HStack,
  IconButton,
  Skeleton,
  Table,
  Text,
} from "@chakra-ui/react"

import { type BuildingPublic, BuildingsService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"

import EditBuilding from "./EditBuilding"

interface BuildingsTableProps {
  buildings: BuildingPublic[]
  isLoading: boolean
}

const BuildingsTable = ({ buildings, isLoading }: BuildingsTableProps) => {
  const queryClient = useQueryClient()
  const navigate = useNavigate()
  const { showSuccessToast } = useCustomToast()
  const [editingBuilding, setEditingBuilding] = useState<BuildingPublic | null>(
    null,
  )

  const deleteMutation = useMutation({
    mutationFn: (id: string) =>
      BuildingsService.deleteBuilding({ buildingId: id }),
    onSuccess: () => {
      showSuccessToast("Building deleted successfully.")
      queryClient.invalidateQueries({ queryKey: ["buildings"] })
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
  })

  const handleDelete = (id: string) => {
    deleteMutation.mutate(id)
  }

  if (isLoading) {
    return (
      <Container maxW="full">
        <Skeleton height="300px" />
      </Container>
    )
  }

  return (
    <>
      <Table.Root size="md">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader>Name</Table.ColumnHeader>
            <Table.ColumnHeader>Address</Table.ColumnHeader>
            <Table.ColumnHeader>Description</Table.ColumnHeader>
            <Table.ColumnHeader>Actions</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {buildings.map((building) => (
            <Table.Row key={building.id}>
              <Table.Cell>{building.name}</Table.Cell>
              <Table.Cell>{building.address || "-"}</Table.Cell>
              <Table.Cell>{building.description || "-"}</Table.Cell>
              <Table.Cell>
                <HStack gap={2} justifyContent="flex-end">
                  <IconButton
                    variant="ghost"
                    colorPalette="blue"
                    aria-label="View floors"
                    size="sm"
                    onClick={() => navigate({
                      to: "/floors",
                      search: { buildingId: building.id },
                    })}
                  >
                    <FaLayerGroup />
                  </IconButton>
                  <IconButton
                    variant="ghost"
                    colorPalette="teal"
                    aria-label="Edit building"
                    size="sm"
                    onClick={() => setEditingBuilding(building)}
                  >
                    <FaEdit />
                  </IconButton>
                  <IconButton
                    variant="ghost"
                    colorPalette="red"
                    aria-label="Delete building"
                    size="sm"
                    onClick={() => handleDelete(building.id)}
                    loading={deleteMutation.isPending}
                  >
                    <FaTrash />
                  </IconButton>
                </HStack>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>

      {editingBuilding && (
        <EditBuilding
          building={editingBuilding}
          isOpen={!!editingBuilding}
          onClose={() => setEditingBuilding(null)}
        />
      )}
    </>
  )
}

export default BuildingsTable
