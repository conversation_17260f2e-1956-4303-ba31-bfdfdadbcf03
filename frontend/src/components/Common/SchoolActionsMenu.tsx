import { HStack, IconButton } from "@chakra-ui/react"
import { useNavigate } from "@tanstack/react-router"
import { FaBuilding } from "react-icons/fa"

import { type SchoolPublic } from "@/client"
import DeleteSchool from "@/components/Schools/DeleteSchool"
import EditSchool from "@/components/Schools/EditSchool"

interface SchoolActionsMenuProps {
  school: SchoolPublic
}

export function SchoolActionsMenu({ school }: SchoolActionsMenuProps) {
  const navigate = useNavigate()

  const handleViewBuildings = () => {
    navigate({
      to: "/buildings",
      search: { schoolId: school.id },
    })
  }

  return (
    <HStack gap={2} justifyContent="flex-end">
      <IconButton
        variant="ghost"
        colorPalette="blue"
        aria-label="View buildings"
        size="sm"
        onClick={handleViewBuildings}
      >
        <FaBuilding />
      </IconButton>
      <EditSchool school={school} />
      <DeleteSchool schoolId={school.id} schoolName={school.name} />
    </HStack>
  )
}
