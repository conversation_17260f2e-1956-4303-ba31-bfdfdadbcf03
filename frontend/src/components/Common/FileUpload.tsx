import { useState, useRef } from "react"
import { Box, Button, Text, VStack, HStack, Image } from "@chakra-ui/react"
import { FaUpload, FaTrash, FaImage } from "react-icons/fa"

interface FileUploadProps {
  onFileSelect: (file: File | null) => void
  accept?: string
  maxSize?: number // in MB
  currentImageUrl?: string | null
  disabled?: boolean
  label?: string
}

const FileUpload = ({
  onFileSelect,
  accept = "image/*",
  maxSize = 10,
  currentImageUrl,
  disabled = false,
  label = "Upload Image"
}: FileUploadProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [dragActive, setDragActive] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return "Please select an image file"
    }

    // Check file size
    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > maxSize) {
      return `File size must be less than ${maxSize}MB`
    }

    return null
  }

  const handleFileSelect = (file: File) => {
    const validationError = validateFile(file)
    if (validationError) {
      setError(validationError)
      return
    }

    setError(null)
    setSelectedFile(file)
    onFileSelect(file)

    // Create preview URL
    const url = URL.createObjectURL(file)
    setPreviewUrl(url)
  }

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (disabled) return

    const file = e.dataTransfer.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleRemoveFile = () => {
    setSelectedFile(null)
    setPreviewUrl(null)
    setError(null)
    onFileSelect(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const handleButtonClick = () => {
    if (disabled) return
    fileInputRef.current?.click()
  }

  const displayImageUrl = previewUrl || currentImageUrl
  const hasImage = !!(selectedFile || currentImageUrl)

  return (
    <VStack gap={3} align="stretch">
      <Text fontSize="sm" fontWeight="medium">
        {label}
      </Text>
      
      {/* File input (hidden) */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileInputChange}
        style={{ display: "none" }}
        disabled={disabled}
      />

      {/* Drop zone / Preview area */}
      <Box
        border="2px dashed"
        borderColor={dragActive ? "blue.400" : "gray.300"}
        borderRadius="md"
        p={4}
        textAlign="center"
        bg={dragActive ? "blue.50" : "gray.50"}
        cursor={disabled ? "not-allowed" : "pointer"}
        transition="all 0.2s"
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={handleButtonClick}
        minH="120px"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        {hasImage ? (
          <VStack gap={2}>
            {displayImageUrl && (
              <Image
                src={displayImageUrl}
                alt="Preview"
                maxH="100px"
                maxW="150px"
                objectFit="cover"
                borderRadius="md"
              />
            )}
            <Text fontSize="sm" color="gray.600">
              {selectedFile ? selectedFile.name : "Current image"}
            </Text>
          </VStack>
        ) : (
          <VStack gap={2}>
            <FaImage size={24} color="gray" />
            <Text fontSize="sm" color="gray.600">
              {dragActive ? "Drop image here" : "Click to upload or drag and drop"}
            </Text>
            <Text fontSize="xs" color="gray.500">
              Max size: {maxSize}MB
            </Text>
          </VStack>
        )}
      </Box>

      {/* Action buttons */}
      <HStack gap={2}>
        <Button
          leftIcon={<FaUpload />}
          size="sm"
          variant="outline"
          onClick={handleButtonClick}
          disabled={disabled}
          flex={1}
        >
          {hasImage ? "Change Image" : "Select Image"}
        </Button>
        
        {hasImage && (
          <Button
            leftIcon={<FaTrash />}
            size="sm"
            variant="outline"
            colorScheme="red"
            onClick={handleRemoveFile}
            disabled={disabled}
          >
            Remove
          </Button>
        )}
      </HStack>

      {/* Error message */}
      {error && (
        <Text fontSize="sm" color="red.500">
          {error}
        </Text>
      )}

      {/* File info */}
      {selectedFile && (
        <Text fontSize="xs" color="gray.500">
          Selected: {selectedFile.name} ({(selectedFile.size / (1024 * 1024)).toFixed(2)}MB)
        </Text>
      )}
    </VStack>
  )
}

export default FileUpload
