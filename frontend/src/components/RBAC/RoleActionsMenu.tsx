import { IconButton } from "@chakra-ui/react"
import { BsThreeDotsVertical } from "react-icons/bs"
import { MenuContent, MenuRoot, MenuTrigger } from "../ui/menu"

import type { RolePublic } from "@/client"
import EditRole from "./EditRole"
import DeleteRole from "./DeleteRole"
import ManageRolePermissions from "./ManageRolePermissions"

interface RoleActionsMenuProps {
  role: RolePublic
}

export const RoleActionsMenu = ({ role }: RoleActionsMenuProps) => {
  return (
    <MenuRoot>
      <MenuTrigger asChild>
        <IconButton variant="ghost" color="inherit">
          <BsThreeDotsVertical />
        </IconButton>
      </MenuTrigger>
      <MenuContent>
        <EditRole role={role} />
        <ManageRolePermissions role={role} />
        <DeleteRole id={role.id} />
      </MenuContent>
    </MenuRoot>
  )
}
