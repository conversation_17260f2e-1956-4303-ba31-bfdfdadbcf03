import { Icon<PERSON>utton } from "@chakra-ui/react"
import { BsThreeDotsVertical } from "react-icons/bs"
import { MenuContent, MenuRoot, MenuTrigger } from "../ui/menu"

import type { UserPublic } from "@/client"
import AssignUserRole from "./AssignUserRole"
import ViewUserPermissions from "./ViewUserPermissions"
import ManageUserRoles from "./ManageUserRoles"

interface UserRoleActionsProps {
  user: UserPublic
}

export const UserRoleActions = ({ user }: UserRoleActionsProps) => {
  return (
    <MenuRoot>
      <MenuTrigger asChild>
        <IconButton variant="ghost" color="inherit">
          <BsThreeDotsVertical />
        </IconButton>
      </MenuTrigger>
      <MenuContent>
        <AssignUserRole user={user} />
        <ManageUserRoles user={user} />
        <ViewUserPermissions user={user} />
      </MenuContent>
    </MenuRoot>
  )
}
