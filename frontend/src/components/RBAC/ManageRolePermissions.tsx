import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { useState } from "react"
import { FaShieldAlt } from "react-icons/fa"

import { type RolePublic, RbacService, type PermissionType } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  Button,
  DialogActionTrigger,
  DialogTitle,
  Text,
  VStack,
  Badge,
  Flex,
  Box,
} from "@chakra-ui/react"
import {
  DialogBody,
  DialogCloseTrigger,
  Dialog<PERSON>ontent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
} from "../ui/dialog"
import { MenuItem } from "../ui/menu"
import { Checkbox } from "../ui/checkbox"

interface ManageRolePermissionsProps {
  role: RolePublic
}

const permissionCategories = {
  "User Management": ["create_user", "read_user", "update_user", "delete_user"],
  "School Management": ["create_school", "read_school", "update_school", "delete_school"],
  "Security": ["view_threats", "manage_threats", "configure_cameras"],
  "System": ["system_config", "view_logs", "manage_roles"],
  "Academic": ["manage_classes", "view_students", "manage_curriculum"],
}

const ManageRolePermissions = ({ role }: ManageRolePermissionsProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()
  const { showSuccessToast: _showSuccessToast } = useCustomToast()

  // Get current role permissions
  const { data: rolePermissions, isLoading } = useQuery({
    queryKey: ["role-permissions", role.id],
    queryFn: () => RbacService.getRolePermissions({ roleId: role.id }),
    enabled: isOpen,
  })

  // Get all available permissions
  const { data: allPermissions } = useQuery({
    queryKey: ["permissions"],
    queryFn: () => RbacService.readPermissions({ limit: 100 }),
    enabled: isOpen,
  })

  const assignPermissionMutation = useMutation({
    mutationFn: (permissionId: string) =>
      RbacService.assignRolePermission({
        roleId: role.id,
        requestBody: { role_id: role.id, permission_id: permissionId },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["role-permissions", role.id] })
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
  })

  const removePermissionMutation = useMutation({
    mutationFn: (permissionId: string) =>
      RbacService.removeRolePermission({
        roleId: role.id,
        permissionId: permissionId,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["role-permissions", role.id] })
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
  })

  const currentPermissions = rolePermissions?.permissions || []
  const permissions = allPermissions?.data || []

  const handlePermissionToggle = (_permission: PermissionType, permissionId: string, isChecked: boolean) => {
    if (isChecked) {
      assignPermissionMutation.mutate(permissionId)
    } else {
      removePermissionMutation.mutate(permissionId)
    }
  }

  const formatRoleName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase()
  }

  const formatPermissionName = (name: string) => {
    return name.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  return (
    <>
      <MenuItem
        value="manage-permissions"
        gap={2}
        py={2}
        style={{ cursor: "pointer" }}
        onClick={() => setIsOpen(true)}
      >
        <FaShieldAlt fontSize="16px" />
        Manage Permissions
      </MenuItem>

      <DialogRoot
        size={{ base: "md", md: "lg" }}
        placement="center"
        open={isOpen}
        onOpenChange={({ open }) => setIsOpen(open)}
      >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Manage Permissions: {formatRoleName(role.name)}</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <Text mb={4}>
            Select the permissions for this role. Changes are saved automatically.
          </Text>

          {isLoading ? (
            <Text>Loading permissions...</Text>
          ) : (
            <VStack gap={4} align="stretch">
              {Object.entries(permissionCategories).map(([category, categoryPermissions]) => (
                <Box key={category}>
                  <Text fontWeight="bold" mb={2} color="blue.600">
                    {category}
                  </Text>
                  <VStack gap={2} align="stretch" pl={4}>
                    {categoryPermissions.map((permissionName) => {
                      const permission = permissions.find(p => p.name === permissionName)
                      if (!permission) return null

                      const isChecked = currentPermissions.includes(permission.name)

                      return (
                        <Flex key={permission.id} align="center" justify="space-between">
                          <Checkbox
                            checked={isChecked}
                            onCheckedChange={({ checked }) =>
                              handlePermissionToggle(permission.name, permission.id, !!checked)
                            }
                            disabled={assignPermissionMutation.isPending || removePermissionMutation.isPending}
                          >
                            <Text>{formatPermissionName(permission.name)}</Text>
                          </Checkbox>
                          {isChecked && (
                            <Badge colorScheme="green" size="sm">
                              Granted
                            </Badge>
                          )}
                        </Flex>
                      )
                    })}
                  </VStack>
                </Box>
              ))}
            </VStack>
          )}
        </DialogBody>
        <DialogFooter>
          <DialogActionTrigger asChild>
            <Button variant="outline">Close</Button>
          </DialogActionTrigger>
        </DialogFooter>
        <DialogCloseTrigger />
      </DialogContent>
      </DialogRoot>
    </>
  )
}

export default ManageRolePermissions
