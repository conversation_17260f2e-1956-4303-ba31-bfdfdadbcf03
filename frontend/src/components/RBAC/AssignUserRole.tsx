import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"
import { useState } from "react"
import { FaUserPlus } from "react-icons/fa"

import { type UserPublic, RbacService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  Button,
  DialogActionTrigger,
  DialogTitle,
  Text,
  VStack,
} from "@chakra-ui/react"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
} from "../ui/dialog"
import { Field } from "../ui/field"
import { MenuItem } from "../ui/menu"

interface AssignUserRoleProps {
  user: UserPublic
}

interface AssignRoleForm {
  roleId: string
}

const AssignUserRole = ({ user }: AssignUserRoleProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<AssignRoleForm>({
    mode: "onBlur",
    criteriaMode: "all",
  })

  // Get all available roles
  const { data: rolesData } = useQuery({
    queryKey: ["roles"],
    queryFn: () => RbacService.readRoles({ limit: 100 }),
    enabled: isOpen,
  })

  // Get user's current roles
  const { data: userRoles } = useQuery({
    queryKey: ["user-roles", user.id],
    queryFn: () => RbacService.getUserRoles({ userId: user.id }),
    enabled: isOpen,
  })

  const mutation = useMutation({
    mutationFn: (data: AssignRoleForm) =>
      RbacService.assignUserRole({
        userId: user.id,
        requestBody: { user_id: user.id, role_id: data.roleId },
      }),
    onSuccess: () => {
      showSuccessToast("Role assigned successfully.")
      reset()
      setIsOpen(false)
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] })
      queryClient.invalidateQueries({ queryKey: ["user-roles", user.id] })
    },
  })

  const onSubmit: SubmitHandler<AssignRoleForm> = (data) => {
    mutation.mutate(data)
  }

  const availableRoles = rolesData?.data || []
  const currentRoleIds = userRoles?.data?.map(role => role.id) || []
  const unassignedRoles = availableRoles.filter(role => !currentRoleIds.includes(role.id))

  const formatRoleName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase()
  }

  return (
    <>
      <MenuItem
        value="assign-role"
        gap={2}
        py={2}
        style={{ cursor: "pointer" }}
        onClick={() => setIsOpen(true)}
      >
        <FaUserPlus fontSize="16px" />
        Assign Role
      </MenuItem>

      <DialogRoot
        size={{ base: "xs", md: "md" }}
        placement="center"
        open={isOpen}
        onOpenChange={({ open }) => setIsOpen(open)}
      >
      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Assign Role to {user.full_name || user.email}</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <Text mb={4}>
              Select a role to assign to this user.
            </Text>
            <VStack gap={4}>
              <Field
                required
                invalid={!!errors.roleId}
                errorText={errors.roleId?.message}
                label="Role"
              >
                <select
                  {...register("roleId", { required: "Role is required" })}
                  style={{
                    width: "100%",
                    padding: "8px",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                  }}
                >
                  <option value="">Select a role</option>
                  {unassignedRoles.length === 0 ? (
                    <option value="" disabled>
                      No available roles to assign
                    </option>
                  ) : (
                    unassignedRoles.map((role) => (
                      <option key={role.id} value={role.id}>
                        {formatRoleName(role.name)}
                        {role.description && ` - ${role.description}`}
                      </option>
                    ))
                  )}
                </select>
              </Field>
            </VStack>
          </DialogBody>
          <DialogFooter>
            <DialogActionTrigger asChild>
              <Button variant="outline">Cancel</Button>
            </DialogActionTrigger>
            <Button
              type="submit"
              colorScheme="blue"
              loading={isSubmitting}
              disabled={isSubmitting || unassignedRoles.length === 0}
            >
              Assign Role
            </Button>
          </DialogFooter>
          <DialogCloseTrigger />
        </form>
      </DialogContent>
      </DialogRoot>
    </>
  )
}

export default AssignUserRole
