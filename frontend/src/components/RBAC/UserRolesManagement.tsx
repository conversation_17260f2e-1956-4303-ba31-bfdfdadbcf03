import { Badge, Container, Flex, Heading, Table, Text, Input } from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { useState } from "react"

import { UsersService } from "@/client"
import { UserRoleActions } from "./UserRoleActions"
import { Skeleton } from "../ui/skeleton"
import {
  PaginationItems,
  PaginationNextTrigger,
  PaginationPrevTrigger,
  PaginationRoot,
} from "@/components/ui/pagination.tsx"

const PER_PAGE = 10

function UsersTable({ searchTerm }: { searchTerm: string }) {
  const [page, setPage] = useState(1)

  const { data, isLoading, isPlaceholderData } = useQuery({
    queryKey: ["users", { page, search: searchTerm }],
    queryFn: () =>
      UsersService.readUsers({
        skip: (page - 1) * PER_PAGE,
        limit: PER_PAGE
      }),
    placeholderData: (prevData) => prevData,
  })

  const users = data?.data ?? []
  const count = data?.count ?? 0

  const filteredUsers = users.filter(user =>
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.full_name && user.full_name.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const formatRoleName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase()
  }

  const getRoleColor = (name: string) => {
    switch (name.toLowerCase()) {
      case "admin":
        return "red"
      case "teachers":
        return "blue"
      case "management":
        return "purple"
      case "security":
        return "orange"
      case "it":
        return "green"
      default:
        return "gray"
    }
  }

  if (isLoading) {
    return (
      <Table.Root size={{ base: "sm", md: "md" }}>
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader w="lg">User</Table.ColumnHeader>
            <Table.ColumnHeader w="lg">Roles</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Status</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Actions</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {Array.from({ length: 5 }).map((_, index) => (
            <Table.Row key={index}>
              <Table.Cell>
                <Skeleton height="40px" width="200px" />
              </Table.Cell>
              <Table.Cell>
                <Skeleton height="20px" width="150px" />
              </Table.Cell>
              <Table.Cell>
                <Skeleton height="20px" width="60px" />
              </Table.Cell>
              <Table.Cell>
                <Skeleton height="20px" width="40px" />
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
    )
  }

  return (
    <>
      <Table.Root size={{ base: "sm", md: "md" }}>
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader w="lg">User</Table.ColumnHeader>
            <Table.ColumnHeader w="lg">Roles</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Status</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Actions</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {filteredUsers?.map((user) => (
            <Table.Row key={user.id} opacity={isPlaceholderData ? 0.5 : 1}>
              <Table.Cell>
                <div>
                  <Text fontWeight="medium">{user.full_name || "No name"}</Text>
                  <Text fontSize="sm" color="gray.600">{user.email}</Text>
                  {user.is_superuser && (
                    <Badge colorScheme="red" size="sm" mt={1}>
                      Superuser
                    </Badge>
                  )}
                </div>
              </Table.Cell>
              <Table.Cell>
                <Flex gap={1} wrap="wrap">
                  {(user as any).roles && (user as any).roles.length > 0 ? (
                    (user as any).roles.map((role: any) => (
                      <Badge
                        key={role.id}
                        colorScheme={getRoleColor(role.name)}
                        variant="subtle"
                        size="sm"
                      >
                        {formatRoleName(role.name)}
                      </Badge>
                    ))
                  ) : (
                    <Text fontSize="sm" color="gray.500">No roles assigned</Text>
                  )}
                </Flex>
              </Table.Cell>
              <Table.Cell>
                <Badge colorScheme={user.is_active ? "green" : "red"} variant="subtle">
                  {user.is_active ? "Active" : "Inactive"}
                </Badge>
              </Table.Cell>
              <Table.Cell>
                <UserRoleActions user={user} />
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>

      {count > PER_PAGE && (
        <Flex justifyContent="flex-end" mt={4}>
          <PaginationRoot
            count={count}
            pageSize={PER_PAGE}
            page={page}
            onPageChange={({ page }) => setPage(page)}
          >
            <Flex>
              <PaginationPrevTrigger />
              <PaginationItems />
              <PaginationNextTrigger />
            </Flex>
          </PaginationRoot>
        </Flex>
      )}
    </>
  )
}

function UserRolesManagement() {
  const [searchTerm, setSearchTerm] = useState("")

  return (
    <Container maxW="full">
      <Flex justify="space-between" align="center" mb={6}>
        <div>
          <Heading size="md">User Roles Assignment</Heading>
          <Text color="gray.600" mt={1}>
            Assign and manage roles for users
          </Text>
        </div>
      </Flex>

      <Flex mb={4}>
        <Input
          placeholder="Search users by name or email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          maxW="400px"
        />
      </Flex>

      <UsersTable searchTerm={searchTerm} />
    </Container>
  )
}

export default UserRolesManagement
