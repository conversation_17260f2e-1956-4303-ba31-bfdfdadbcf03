import { Table } from "@chakra-ui/react"
import { Skeleton } from "../ui/skeleton"

const PendingRoles = () => {
  return (
    <Table.Root size={{ base: "sm", md: "md" }}>
      <Table.Header>
        <Table.Row>
          <Table.ColumnHeader w="sm">Role Name</Table.ColumnHeader>
          <Table.ColumnHeader w="lg">Description</Table.ColumnHeader>
          <Table.ColumnHeader w="sm">Status</Table.ColumnHeader>
          <Table.ColumnHeader w="sm">Created</Table.ColumnHeader>
          <Table.ColumnHeader w="sm">Actions</Table.ColumnHeader>
        </Table.Row>
      </Table.Header>
      <Table.Body>
        {Array.from({ length: 5 }).map((_, index) => (
          <Table.Row key={index}>
            <Table.Cell>
              <Skeleton height="20px" width="80px" />
            </Table.Cell>
            <Table.Cell>
              <Skeleton height="20px" width="200px" />
            </Table.Cell>
            <Table.Cell>
              <Skeleton height="20px" width="60px" />
            </Table.Cell>
            <Table.Cell>
              <Skeleton height="20px" width="100px" />
            </Table.Cell>
            <Table.Cell>
              <Skeleton height="20px" width="40px" />
            </Table.Cell>
          </Table.Row>
        ))}
      </Table.Body>
    </Table.Root>
  )
}

export default PendingRoles
