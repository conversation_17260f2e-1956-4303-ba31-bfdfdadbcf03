import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useState } from "react"
import { FaTrash } from "react-icons/fa"

import { RbacService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  Button,
  DialogActionTrigger,
  DialogTitle,
  Text,
} from "@chakra-ui/react"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
} from "../ui/dialog"
import { MenuItem } from "../ui/menu"

interface DeleteRoleProps {
  id: string
}

const DeleteRole = ({ id }: DeleteRoleProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()

  const mutation = useMutation({
    mutationFn: () => RbacService.deleteRole({ roleId: id }),
    onSuccess: () => {
      showSuccessToast("Role deleted successfully.")
      setIsOpen(false)
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] })
    },
  })

  return (
    <>
      <MenuItem
        value="delete-role"
        gap={2}
        py={2}
        style={{ cursor: "pointer" }}
        color="red.500"
        onClick={() => setIsOpen(true)}
      >
        <FaTrash fontSize="16px" />
        Delete Role
      </MenuItem>

      <DialogRoot
        size={{ base: "xs", md: "md" }}
        placement="center"
        open={isOpen}
        onOpenChange={({ open }) => setIsOpen(open)}
      >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Role</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <Text>
            Are you sure you want to delete this role? This action cannot be undone.
            All users assigned to this role will lose their permissions.
          </Text>
        </DialogBody>
        <DialogFooter>
          <DialogActionTrigger asChild>
            <Button variant="outline">Cancel</Button>
          </DialogActionTrigger>
          <Button
            colorScheme="red"
            onClick={() => mutation.mutate()}
            loading={mutation.isPending}
            disabled={mutation.isPending}
          >
            Delete Role
          </Button>
        </DialogFooter>
        <DialogCloseTrigger />
      </DialogContent>
      </DialogRoot>
    </>
  )
}

export default DeleteRole
