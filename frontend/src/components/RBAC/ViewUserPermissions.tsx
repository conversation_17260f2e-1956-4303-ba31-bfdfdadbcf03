import { useQuery } from "@tanstack/react-query"
import { useState } from "react"
import { FaEye } from "react-icons/fa"

import { type UserPublic, RbacService } from "@/client"
import {
  <PERSON><PERSON>,
  DialogActionTrigger,
  DialogTitle,
  Text,
  VStack,
  Badge,
  Box,
  SimpleGrid,
} from "@chakra-ui/react"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
} from "../ui/dialog"
import { MenuItem } from "../ui/menu"

interface ViewUserPermissionsProps {
  user: UserPublic
}

const permissionCategories = {
  "User Management": ["create_user", "read_user", "update_user", "delete_user"],
  "School Management": ["create_school", "read_school", "update_school", "delete_school"],
  "Security": ["view_threats", "manage_threats", "configure_cameras"],
  "System": ["system_config", "view_logs", "manage_roles"],
  "Academic": ["manage_classes", "view_students", "manage_curriculum"],
}

const ViewUserPermissions = ({ user }: ViewUserPermissionsProps) => {
  const [isOpen, setIsOpen] = useState(false)

  // Get user's permissions
  const { data: userPermissions, isLoading } = useQuery({
    queryKey: ["user-permissions", user.id],
    queryFn: () => RbacService.getUserPermissions({ userId: user.id }),
    enabled: isOpen,
  })

  const formatPermissionName = (name: string) => {
    return name.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const getPermissionColor = (name: string) => {
    if (name.includes('create') || name.includes('delete')) return "red"
    if (name.includes('update') || name.includes('manage')) return "orange"
    if (name.includes('read') || name.includes('view')) return "green"
    if (name.includes('system') || name.includes('config')) return "purple"
    return "blue"
  }

  const currentPermissions = userPermissions?.permissions || []

  return (
    <>
      <MenuItem
        value="view-permissions"
        gap={2}
        py={2}
        style={{ cursor: "pointer" }}
        onClick={() => setIsOpen(true)}
      >
        <FaEye fontSize="16px" />
        View Permissions
      </MenuItem>

      <DialogRoot
        size={{ base: "md", md: "lg" }}
        placement="center"
        open={isOpen}
        onOpenChange={({ open }) => setIsOpen(open)}
      >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>User Permissions: {user.full_name || user.email}</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <Text mb={4}>
            All permissions granted to this user through their assigned roles.
          </Text>

          {user.is_superuser && (
            <Box mb={4} p={3} bg="red.50" borderRadius="md" border="1px solid" borderColor="red.200">
              <Badge colorScheme="red" mb={2}>SUPERUSER</Badge>
              <Text fontSize="sm" color="red.700">
                This user has superuser privileges and can access all system features regardless of role permissions.
              </Text>
            </Box>
          )}

          {isLoading ? (
            <Text>Loading permissions...</Text>
          ) : currentPermissions.length === 0 ? (
            <Text color="gray.500">No permissions assigned to this user.</Text>
          ) : (
            <VStack gap={4} align="stretch">
              {Object.entries(permissionCategories).map(([category, categoryPermissions]) => {
                const userCategoryPermissions = categoryPermissions.filter(permission =>
                  currentPermissions.includes(permission as any)
                )

                if (userCategoryPermissions.length === 0) return null

                return (
                  <Box key={category}>
                    <Text fontWeight="bold" mb={2} color="blue.600">
                      {category}
                    </Text>
                    <SimpleGrid columns={{ base: 1, md: 2 }} gap={2} pl={4}>
                      {userCategoryPermissions.map((permission) => (
                        <Badge
                          key={permission}
                          colorScheme={getPermissionColor(permission)}
                          variant="subtle"
                          size="sm"
                          p={2}
                        >
                          {formatPermissionName(permission)}
                        </Badge>
                      ))}
                    </SimpleGrid>
                  </Box>
                )
              })}
            </VStack>
          )}

          {currentPermissions.length > 0 && (
            <Box mt={4} p={3} bg="blue.50" borderRadius="md">
              <Text fontSize="sm" color="blue.700">
                Total permissions: {currentPermissions.length}
              </Text>
            </Box>
          )}
        </DialogBody>
        <DialogFooter>
          <DialogActionTrigger asChild>
            <Button variant="outline">Close</Button>
          </DialogActionTrigger>
        </DialogFooter>
        <DialogCloseTrigger />
      </DialogContent>
      </DialogRoot>
    </>
  )
}

export default ViewUserPermissions
