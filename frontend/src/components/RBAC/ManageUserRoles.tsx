import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { useState } from "react"
import { FaUserCog } from "react-icons/fa"

import { type UserPublic, RbacService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  Button,
  DialogActionTrigger,
  DialogTitle,
  Text,
  VStack,
  Badge,
  Flex,
  IconButton,
} from "@chakra-ui/react"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
} from "../ui/dialog"
import { MenuItem } from "../ui/menu"
import { FaTrash } from "react-icons/fa"

interface ManageUserRolesProps {
  user: UserPublic
}

const ManageUserRoles = ({ user }: ManageUserRolesProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()

  // Get user's current roles
  const { data: userRoles, isLoading } = useQuery({
    queryKey: ["user-roles", user.id],
    queryFn: () => RbacService.getUserRoles({ userId: user.id }),
    enabled: isOpen,
  })

  const removeRoleMutation = useMutation({
    mutationFn: (roleId: string) =>
      RbacService.removeUserRole({
        userId: user.id,
        roleId: roleId,
      }),
    onSuccess: () => {
      showSuccessToast("Role removed successfully.")
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] })
      queryClient.invalidateQueries({ queryKey: ["user-roles", user.id] })
    },
  })

  const formatRoleName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase()
  }

  const getRoleColor = (name: string) => {
    switch (name.toLowerCase()) {
      case "admin":
        return "red"
      case "teachers":
        return "blue"
      case "management":
        return "purple"
      case "security":
        return "orange"
      case "it":
        return "green"
      default:
        return "gray"
    }
  }

  const currentRoles = userRoles?.data || []

  return (
    <>
      <MenuItem
        value="manage-roles"
        gap={2}
        py={2}
        style={{ cursor: "pointer" }}
        onClick={() => setIsOpen(true)}
      >
        <FaUserCog fontSize="16px" />
        Manage Roles
      </MenuItem>

      <DialogRoot
        size={{ base: "md", md: "lg" }}
        placement="center"
        open={isOpen}
        onOpenChange={({ open }) => setIsOpen(open)}
      >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Manage Roles: {user.full_name || user.email}</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <Text mb={4}>
            Current roles assigned to this user. Click the remove button to unassign a role.
          </Text>

          {isLoading ? (
            <Text>Loading roles...</Text>
          ) : currentRoles.length === 0 ? (
            <Text color="gray.500">No roles assigned to this user.</Text>
          ) : (
            <VStack gap={3} align="stretch">
              {currentRoles.map((role) => (
                <Flex
                  key={role.id}
                  align="center"
                  justify="space-between"
                  p={3}
                  border="1px solid"
                  borderColor="gray.200"
                  borderRadius="md"
                >
                  <Flex align="center" gap={3}>
                    <Badge
                      colorScheme={getRoleColor(role.name)}
                      variant="subtle"
                      size="lg"
                    >
                      {formatRoleName(role.name)}
                    </Badge>
                    <div>
                      <Text fontWeight="medium">{formatRoleName(role.name)}</Text>
                      {role.description && (
                        <Text fontSize="sm" color="gray.600">
                          {role.description}
                        </Text>
                      )}
                    </div>
                  </Flex>
                  <IconButton
                    variant="ghost"
                    colorScheme="red"
                    size="sm"
                    onClick={() => removeRoleMutation.mutate(role.id)}
                    loading={removeRoleMutation.isPending}
                    disabled={removeRoleMutation.isPending}
                  >
                    <FaTrash />
                  </IconButton>
                </Flex>
              ))}
            </VStack>
          )}
        </DialogBody>
        <DialogFooter>
          <DialogActionTrigger asChild>
            <Button variant="outline">Close</Button>
          </DialogActionTrigger>
        </DialogFooter>
        <DialogCloseTrigger />
      </DialogContent>
      </DialogRoot>
    </>
  )
}

export default ManageUserRoles
