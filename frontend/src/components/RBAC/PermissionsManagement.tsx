import { Badge, Container, Flex, Heading, Table, Text } from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"

import { RbacService } from "@/client"
import { Skeleton } from "../ui/skeleton"

const PER_PAGE = 50

function PermissionsTable() {
  const { data, isLoading } = useQuery({
    queryKey: ["permissions"],
    queryFn: () => RbacService.readPermissions({ limit: PER_PAGE }),
  })

  const permissions = data?.data ?? []

  const formatPermissionName = (name: string) => {
    return name.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const getPermissionColor = (name: string) => {
    if (name.includes('create') || name.includes('delete')) return "red"
    if (name.includes('update') || name.includes('manage')) return "orange"
    if (name.includes('read') || name.includes('view')) return "green"
    if (name.includes('system') || name.includes('config')) return "purple"
    return "blue"
  }

  const getResourceBadge = (name: string) => {
    if (name.includes('user')) return "Users"
    if (name.includes('school')) return "Schools"
    if (name.includes('threat')) return "Security"
    if (name.includes('camera')) return "Cameras"
    if (name.includes('system') || name.includes('config') || name.includes('logs')) return "System"
    if (name.includes('class') || name.includes('student') || name.includes('curriculum')) return "Academic"
    return "General"
  }

  if (isLoading) {
    return (
      <Table.Root size={{ base: "sm", md: "md" }}>
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader w="lg">Permission Name</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Resource</Table.ColumnHeader>
            <Table.ColumnHeader w="lg">Description</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Created</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {Array.from({ length: 10 }).map((_, index) => (
            <Table.Row key={index}>
              <Table.Cell>
                <Skeleton height="20px" width="150px" />
              </Table.Cell>
              <Table.Cell>
                <Skeleton height="20px" width="80px" />
              </Table.Cell>
              <Table.Cell>
                <Skeleton height="20px" width="200px" />
              </Table.Cell>
              <Table.Cell>
                <Skeleton height="20px" width="100px" />
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
    )
  }

  return (
    <Table.Root size={{ base: "sm", md: "md" }}>
      <Table.Header>
        <Table.Row>
          <Table.ColumnHeader w="lg">Permission Name</Table.ColumnHeader>
          <Table.ColumnHeader w="sm">Resource</Table.ColumnHeader>
          <Table.ColumnHeader w="lg">Description</Table.ColumnHeader>
          <Table.ColumnHeader w="sm">Created</Table.ColumnHeader>
        </Table.Row>
      </Table.Header>
      <Table.Body>
        {permissions?.map((permission) => (
          <Table.Row key={permission.id}>
            <Table.Cell>
              <Badge colorScheme={getPermissionColor(permission.name)} variant="subtle">
                {formatPermissionName(permission.name)}
              </Badge>
            </Table.Cell>
            <Table.Cell>
              <Badge variant="outline" size="sm">
                {getResourceBadge(permission.name)}
              </Badge>
            </Table.Cell>
            <Table.Cell color={!permission.description ? "gray" : "inherit"}>
              {permission.description || "No description"}
            </Table.Cell>
            <Table.Cell>
              {new Date(permission.created_at).toLocaleDateString()}
            </Table.Cell>
          </Table.Row>
        ))}
      </Table.Body>
    </Table.Root>
  )
}

function PermissionsManagement() {
  return (
    <Container maxW="full">
      <Flex justify="space-between" align="center" mb={6}>
        <div>
          <Heading size="md">Permissions Management</Heading>
          <Text color="gray.600" mt={1}>
            View all available system permissions
          </Text>
        </div>
      </Flex>
      <PermissionsTable />
    </Container>
  )
}

export default PermissionsManagement
