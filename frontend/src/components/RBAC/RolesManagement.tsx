import { Badge, Container, Flex, Heading, Table, Text } from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"

import { RbacService } from "@/client"
import AddRole from "./AddRole"
import { RoleActionsMenu } from "./RoleActionsMenu"
import PendingRoles from "./PendingRoles"
import {
  PaginationItems,
  PaginationNextTrigger,
  PaginationPrevTrigger,
  PaginationRoot,
} from "@/components/ui/pagination.tsx"

const PER_PAGE = 10

function getRolesQueryOptions({ page }: { page: number }) {
  return {
    queryFn: () =>
      RbacService.readRoles({ skip: (page - 1) * PER_PAGE, limit: PER_PAGE }),
    queryKey: ["roles", { page }],
  }
}

function RolesTable() {
  const page = 1 // For now, we'll use a simple pagination

  const { data, isLoading, isPlaceholderData } = useQuery({
    ...getRolesQueryOptions({ page }),
    placeholderData: (prevData) => prevData,
  })

  const roles = data?.data ?? []
  const count = data?.count ?? 0

  if (isLoading) {
    return <PendingRoles />
  }

  const formatRoleName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase()
  }

  const getRoleColor = (name: string) => {
    switch (name.toLowerCase()) {
      case "admin":
        return "red"
      case "teachers":
        return "blue"
      case "management":
        return "purple"
      case "security":
        return "orange"
      case "it":
        return "green"
      default:
        return "gray"
    }
  }

  return (
    <>
      <Table.Root size={{ base: "sm", md: "md" }}>
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader w="sm">Role Name</Table.ColumnHeader>
            <Table.ColumnHeader w="lg">Description</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Status</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Created</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Actions</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {roles?.map((role) => (
            <Table.Row key={role.id} opacity={isPlaceholderData ? 0.5 : 1}>
              <Table.Cell>
                <Badge colorScheme={getRoleColor(role.name)} variant="subtle">
                  {formatRoleName(role.name)}
                </Badge>
              </Table.Cell>
              <Table.Cell color={!role.description ? "gray" : "inherit"}>
                {role.description || "No description"}
              </Table.Cell>
              <Table.Cell>
                <Badge colorScheme={role.is_active ? "green" : "red"} variant="subtle">
                  {role.is_active ? "Active" : "Inactive"}
                </Badge>
              </Table.Cell>
              <Table.Cell>
                {new Date(role.created_at).toLocaleDateString()}
              </Table.Cell>
              <Table.Cell>
                <RoleActionsMenu role={role} />
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>

      {count > PER_PAGE && (
        <Flex justifyContent="flex-end" mt={4}>
          <PaginationRoot
            count={count}
            pageSize={PER_PAGE}
            onPageChange={({ page: _page }) => {
              // Handle pagination
            }}
          >
            <Flex>
              <PaginationPrevTrigger />
              <PaginationItems />
              <PaginationNextTrigger />
            </Flex>
          </PaginationRoot>
        </Flex>
      )}
    </>
  )
}

function RolesManagement() {
  return (
    <Container maxW="full">
      <Flex justify="space-between" align="center" mb={6}>
        <div>
          <Heading size="md">Roles Management</Heading>
          <Text color="gray.600" mt={1}>
            Manage system roles and their permissions
          </Text>
        </div>
        <AddRole />
      </Flex>
      <RolesTable />
    </Container>
  )
}

export default RolesManagement
