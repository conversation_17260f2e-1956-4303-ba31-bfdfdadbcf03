import { useMutation, useQueryClient } from "@tanstack/react-query"
import { Controller, type SubmitHandler, useForm } from "react-hook-form"
import { useState } from "react"
import { FaEdit } from "react-icons/fa"

import { type RolePublic, type RoleUpdate, RbacService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  Button,
  DialogActionTrigger,
  DialogTitle,
  Input,
  Text,
  VStack,
} from "@chakra-ui/react"
import { Checkbox } from "../ui/checkbox"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
} from "../ui/dialog"
import { Field } from "../ui/field"
import { MenuItem } from "../ui/menu"

interface EditRoleProps {
  role: RolePublic
}

const EditRole = ({ role }: EditRoleProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()
  const {
    control,
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<RoleUpdate>({
    mode: "onBlur",
    criteriaMode: "all",
    defaultValues: {
      description: role.description || "",
      is_active: role.is_active,
    },
  })

  const mutation = useMutation({
    mutationFn: (data: RoleUpdate) =>
      RbacService.updateRole({ roleId: role.id, requestBody: data }),
    onSuccess: () => {
      showSuccessToast("Role updated successfully.")
      reset()
      setIsOpen(false)
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] })
    },
  })

  const onSubmit: SubmitHandler<RoleUpdate> = (data) => {
    mutation.mutate(data)
  }

  const formatRoleName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase()
  }

  return (
    <>
      <MenuItem
        value="edit-role"
        gap={2}
        py={2}
        style={{ cursor: "pointer" }}
        onClick={() => setIsOpen(true)}
      >
        <FaEdit fontSize="16px" />
        Edit Role
      </MenuItem>

      <DialogRoot
        size={{ base: "xs", md: "md" }}
        placement="center"
        open={isOpen}
        onOpenChange={({ open }) => setIsOpen(open)}
      >
      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Edit Role: {formatRoleName(role.name)}</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <Text mb={4}>Update the role details below.</Text>
            <VStack gap={4}>
              <Field
                invalid={!!errors.description}
                errorText={errors.description?.message}
                label="Description"
              >
                <Input
                  id="description"
                  {...register("description")}
                  placeholder="Role description"
                  type="text"
                />
              </Field>

              <Field label="Status">
                <Controller
                  name="is_active"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      checked={field.value || false}
                      onCheckedChange={({ checked }) => field.onChange(checked)}
                    >
                      Active role
                    </Checkbox>
                  )}
                />
              </Field>
            </VStack>
          </DialogBody>
          <DialogFooter>
            <DialogActionTrigger asChild>
              <Button variant="outline">Cancel</Button>
            </DialogActionTrigger>
            <Button
              type="submit"
              colorScheme="blue"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              Update Role
            </Button>
          </DialogFooter>
          <DialogCloseTrigger />
        </form>
      </DialogContent>
      </DialogRoot>
    </>
  )
}

export default EditRole
