/**
 * Hook to get environment configuration
 * This would typically come from environment variables or API
 */
export const useEnvironment = () => {
  // In a real application, this would come from environment variables
  // For now, we'll assume local environment
  const cloudEnvironment = import.meta.env.VITE_CLOUD_ENVIRONMENT || 'local'
  
  return {
    cloudEnvironment,
    isLocal: cloudEnvironment === 'local',
    isAWS: cloudEnvironment === 'aws',
  }
}
