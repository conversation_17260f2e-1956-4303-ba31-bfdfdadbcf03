#!/usr/bin/env python3
"""
Test script for RBAC system.
This script tests the basic functionality of the RBAC system.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlmodel import Session
from app.core.db import engine
from app.core.rbac import (
    initialize_default_roles_and_permissions,
    assign_role_to_user,
    user_has_permission,
    user_has_role,
    get_user_permissions,
    get_user_roles
)
from app.models import User, Role, RoleType, PermissionType
from app import crud
from app.models import UserCreate


def test_rbac_system():
    """Test the RBAC system functionality."""
    print("🔐 Testing RBAC System")
    print("=" * 50)

    with Session(engine) as session:
        # Initialize RBAC system
        print("1. Initializing RBAC system...")
        try:
            initialize_default_roles_and_permissions(session)
            print("   ✅ RBAC system initialized successfully")
        except Exception as e:
            print(f"   ❌ Error initializing RBAC: {e}")
            return False

        # Check if roles were created
        print("\n2. Checking created roles...")
        from sqlmodel import select
        roles = session.exec(select(Role)).all()
        expected_roles = [RoleType.ADMIN, RoleType.TEACHERS, RoleType.MANAGEMENT, RoleType.SECURITY, RoleType.IT]

        for role_type in expected_roles:
            role = session.exec(select(Role).where(Role.name == role_type)).first()
            if role:
                print(f"   ✅ {role_type.value} role created")
            else:
                print(f"   ❌ {role_type.value} role missing")
                return False

        # Create a test user
        print("\n3. Creating test user...")
        try:
            # Check if user already exists
            existing_user = crud.get_user_by_email(session=session, email="<EMAIL>")
            if existing_user:
                test_user = existing_user
                print(f"   ✅ Test user already exists: {test_user.email}")
            else:
                test_user = crud.create_user(
                    session=session,
                    user_create=UserCreate(
                        email="<EMAIL>",
                        password="testpassword123",
                        full_name="Test User"
                    )
                )
                print(f"   ✅ Test user created: {test_user.email}")
        except Exception as e:
            print(f"   ❌ Error creating test user: {e}")
            return False

        # Assign teacher role to test user
        print("\n4. Assigning TEACHERS role to test user...")
        try:
            teacher_role = session.exec(select(Role).where(Role.name == RoleType.TEACHERS)).first()
            if teacher_role:
                try:
                    assign_role_to_user(session, test_user.id, teacher_role.id)
                    print("   ✅ TEACHERS role assigned successfully")
                except Exception as role_error:
                    if "already has this role" in str(role_error):
                        print("   ✅ TEACHERS role already assigned")
                    else:
                        raise role_error
            else:
                print("   ❌ TEACHERS role not found")
                return False
        except Exception as e:
            print(f"   ❌ Error assigning role: {e}")
            return False

        # Test role checking
        print("\n5. Testing role checks...")
        has_teacher_role = user_has_role(session, test_user.id, RoleType.TEACHERS)
        has_admin_role = user_has_role(session, test_user.id, RoleType.ADMIN)

        if has_teacher_role:
            print("   ✅ User has TEACHERS role")
        else:
            print("   ❌ User should have TEACHERS role")
            return False

        if not has_admin_role:
            print("   ✅ User does not have ADMIN role (correct)")
        else:
            print("   ❌ User should not have ADMIN role")
            return False

        # Test permission checking
        print("\n6. Testing permission checks...")
        can_manage_classes = user_has_permission(session, test_user.id, PermissionType.MANAGE_CLASSES)
        can_delete_users = user_has_permission(session, test_user.id, PermissionType.DELETE_USER)

        if can_manage_classes:
            print("   ✅ Teacher can manage classes (correct)")
        else:
            print("   ❌ Teacher should be able to manage classes")
            return False

        if not can_delete_users:
            print("   ✅ Teacher cannot delete users (correct)")
        else:
            print("   ❌ Teacher should not be able to delete users")
            return False

        # Test getting user permissions
        print("\n7. Testing user permissions retrieval...")
        user_permissions = get_user_permissions(session, test_user.id)
        expected_teacher_permissions = [
            PermissionType.READ_USER,
            PermissionType.READ_SCHOOL,
            PermissionType.VIEW_THREATS,
            PermissionType.MANAGE_CLASSES,
            PermissionType.VIEW_STUDENTS,
            PermissionType.MANAGE_CURRICULUM,
        ]

        print(f"   User has {len(user_permissions)} permissions:")
        for permission in user_permissions:
            print(f"     - {permission.value}")

        missing_permissions = set(expected_teacher_permissions) - user_permissions
        if not missing_permissions:
            print("   ✅ All expected teacher permissions present")
        else:
            print(f"   ❌ Missing permissions: {[p.value for p in missing_permissions]}")
            return False

        # Test getting user roles
        print("\n8. Testing user roles retrieval...")
        user_roles = get_user_roles(session, test_user.id)
        print(f"   User has {len(user_roles)} roles:")
        for role in user_roles:
            print(f"     - {role.name.value}")

        if len(user_roles) == 1 and user_roles[0].name == RoleType.TEACHERS:
            print("   ✅ User has correct roles")
        else:
            print("   ❌ User roles are incorrect")
            return False

        print("\n" + "=" * 50)
        print("🎉 All RBAC tests passed successfully!")
        print("\nThe RBAC system is working correctly with:")
        print("- 5 predefined roles (Admin, Teachers, Management, Security, IT)")
        print("- 13 granular permissions")
        print("- Role-permission mappings")
        print("- User-role assignments")
        print("- Permission checking functions")

        return True


if __name__ == "__main__":
    success = test_rbac_system()
    if not success:
        print("\n❌ RBAC tests failed!")
        sys.exit(1)
    else:
        print("\n✅ RBAC system is ready to use!")
        sys.exit(0)
