"""
Role-Based Access Control (RBAC) utilities and functions.
"""

import uuid
from typing import Any, Callable, List, Set
from functools import wraps

from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
from sqlmodel import Session, select

from app.models import (
    User, Role, Permission, UserRole, RolePermission,
    RoleType, PermissionType
)


class RBACError(Exception):
    """Custom exception for RBAC-related errors."""
    pass


def get_user_roles(session: Session, user_id: uuid.UUID) -> List[Role]:
    """Get all roles assigned to a user."""
    statement = (
        select(Role)
        .join(UserRole)
        .where(UserRole.user_id == user_id)
        .where(Role.is_active == True)
    )
    return list(session.exec(statement))


def get_user_permissions(session: Session, user_id: uuid.UUID) -> Set[PermissionType]:
    """Get all permissions for a user based on their roles."""
    statement = (
        select(Permission.name)
        .join(RolePermission)
        .join(Role)
        .join(UserRole)
        .where(UserRole.user_id == user_id)
        .where(Role.is_active == True)
    )
    permissions = session.exec(statement)
    return set(permissions)


def get_role_permissions(session: Session, role_id: uuid.UUID) -> Set[PermissionType]:
    """Get all permissions for a specific role."""
    statement = (
        select(Permission.name)
        .join(RolePermission)
        .where(RolePermission.role_id == role_id)
    )
    permissions = session.exec(statement)
    return set(permissions)


def user_has_permission(session: Session, user_id: uuid.UUID, permission: PermissionType) -> bool:
    """Check if a user has a specific permission."""
    user_permissions = get_user_permissions(session, user_id)
    return permission in user_permissions


def user_has_role(session: Session, user_id: uuid.UUID, role: RoleType) -> bool:
    """Check if a user has a specific role."""
    statement = (
        select(UserRole)
        .join(Role)
        .where(UserRole.user_id == user_id)
        .where(Role.name == role)
        .where(Role.is_active == True)
    )
    result = session.exec(statement).first()
    return result is not None


def user_has_any_role(session: Session, user_id: uuid.UUID, roles: List[RoleType]) -> bool:
    """Check if a user has any of the specified roles."""
    return any(user_has_role(session, user_id, role) for role in roles)


def assign_role_to_user(
    session: Session,
    user_id: uuid.UUID,
    role_id: uuid.UUID,
    assigned_by: uuid.UUID | None = None
) -> UserRole:
    """Assign a role to a user."""
    # Check if assignment already exists
    existing = session.exec(
        select(UserRole).where(
            UserRole.user_id == user_id,
            UserRole.role_id == role_id
        )
    ).first()

    if existing:
        raise RBACError("User already has this role")

    user_role = UserRole(
        user_id=user_id,
        role_id=role_id,
        assigned_by=assigned_by
    )
    session.add(user_role)
    session.commit()
    session.refresh(user_role)
    return user_role


def remove_role_from_user(session: Session, user_id: uuid.UUID, role_id: uuid.UUID) -> bool:
    """Remove a role from a user."""
    user_role = session.exec(
        select(UserRole).where(
            UserRole.user_id == user_id,
            UserRole.role_id == role_id
        )
    ).first()

    if not user_role:
        return False

    session.delete(user_role)
    session.commit()
    return True


def assign_permission_to_role(session: Session, role_id: uuid.UUID, permission_id: uuid.UUID) -> RolePermission:
    """Assign a permission to a role."""
    # Check if assignment already exists
    existing = session.exec(
        select(RolePermission).where(
            RolePermission.role_id == role_id,
            RolePermission.permission_id == permission_id
        )
    ).first()

    if existing:
        raise RBACError("Role already has this permission")

    role_permission = RolePermission(
        role_id=role_id,
        permission_id=permission_id
    )
    session.add(role_permission)
    session.commit()
    session.refresh(role_permission)
    return role_permission


def remove_permission_from_role(session: Session, role_id: uuid.UUID, permission_id: uuid.UUID) -> bool:
    """Remove a permission from a role."""
    role_permission = session.exec(
        select(RolePermission).where(
            RolePermission.role_id == role_id,
            RolePermission.permission_id == permission_id
        )
    ).first()

    if not role_permission:
        return False

    session.delete(role_permission)
    session.commit()
    return True


# Default role permissions mapping
DEFAULT_ROLE_PERMISSIONS = {
    RoleType.ADMIN: [
        PermissionType.CREATE_USER,
        PermissionType.READ_USER,
        PermissionType.UPDATE_USER,
        PermissionType.DELETE_USER,
        PermissionType.CREATE_SCHOOL,
        PermissionType.READ_SCHOOL,
        PermissionType.UPDATE_SCHOOL,
        PermissionType.DELETE_SCHOOL,
        PermissionType.VIEW_THREATS,
        PermissionType.MANAGE_THREATS,
        PermissionType.CONFIGURE_CAMERAS,
        PermissionType.SYSTEM_CONFIG,
        PermissionType.VIEW_LOGS,
        PermissionType.MANAGE_ROLES,
        PermissionType.MANAGE_CLASSES,
        PermissionType.VIEW_STUDENTS,
        PermissionType.MANAGE_CURRICULUM,
    ],
    RoleType.TEACHERS: [
        PermissionType.READ_USER,
        PermissionType.READ_SCHOOL,
        PermissionType.VIEW_THREATS,
        PermissionType.MANAGE_CLASSES,
        PermissionType.VIEW_STUDENTS,
        PermissionType.MANAGE_CURRICULUM,
    ],
    RoleType.MANAGEMENT: [
        PermissionType.READ_USER,
        PermissionType.UPDATE_USER,
        PermissionType.READ_SCHOOL,
        PermissionType.UPDATE_SCHOOL,
        PermissionType.VIEW_THREATS,
        PermissionType.MANAGE_THREATS,
        PermissionType.VIEW_LOGS,
        PermissionType.MANAGE_CLASSES,
        PermissionType.VIEW_STUDENTS,
    ],
    RoleType.SECURITY: [
        PermissionType.READ_USER,
        PermissionType.READ_SCHOOL,
        PermissionType.VIEW_THREATS,
        PermissionType.MANAGE_THREATS,
        PermissionType.CONFIGURE_CAMERAS,
        PermissionType.VIEW_LOGS,
    ],
    RoleType.IT: [
        PermissionType.READ_USER,
        PermissionType.UPDATE_USER,
        PermissionType.READ_SCHOOL,
        PermissionType.UPDATE_SCHOOL,
        PermissionType.VIEW_THREATS,
        PermissionType.CONFIGURE_CAMERAS,
        PermissionType.SYSTEM_CONFIG,
        PermissionType.VIEW_LOGS,
    ],
}


def initialize_default_roles_and_permissions(session: Session) -> None:
    """Initialize default roles and permissions in the database."""
    # Create all permissions first
    for permission_type in PermissionType:
        existing_permission = session.exec(
            select(Permission).where(Permission.name == permission_type)
        ).first()

        if not existing_permission:
            permission = Permission(
                name=permission_type,
                description=f"Permission to {permission_type.value.replace('_', ' ')}",
                resource=permission_type.value.split('_')[-1] if '_' in permission_type.value else None
            )
            session.add(permission)

    session.commit()

    # Create all roles
    for role_type in RoleType:
        existing_role = session.exec(
            select(Role).where(Role.name == role_type)
        ).first()

        if not existing_role:
            role = Role(
                name=role_type,
                description=f"{role_type.value.title()} role with predefined permissions"
            )
            session.add(role)
            session.commit()
            session.refresh(role)

            # Assign default permissions to role
            permissions_for_role = DEFAULT_ROLE_PERMISSIONS.get(role_type, [])
            for permission_type in permissions_for_role:
                permission = session.exec(
                    select(Permission).where(Permission.name == permission_type)
                ).first()

                if permission:
                    try:
                        assign_permission_to_role(session, role.id, permission.id)
                    except RBACError:
                        # Permission already assigned, skip
                        pass


# Decorators and dependency functions
def require_permission(permission: PermissionType):
    """Decorator to require a specific permission for an endpoint."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # This will be used with FastAPI dependencies
            return func(*args, **kwargs)
        wrapper._required_permission = permission
        return wrapper
    return decorator


def require_role(role: RoleType):
    """Decorator to require a specific role for an endpoint."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        wrapper._required_role = role
        return wrapper
    return decorator


def require_any_role(roles: List[RoleType]):
    """Decorator to require any of the specified roles for an endpoint."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        wrapper._required_roles = roles
        return wrapper
    return decorator
