from sqlmodel import Session, create_engine, select

from app import crud
from app.core.config import settings
from app.core.rbac import initialize_default_roles_and_permissions, assign_role_to_user
from app.models import User, UserCreate, Role, RoleType

engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))


# make sure all SQLModel models are imported (app.models) before initializing DB
# otherwise, SQLModel might fail to initialize relationships properly
# for more details: https://github.com/fastapi/full-stack-fastapi-template/issues/28


def init_db(session: Session) -> None:
    # Tables should be created with Alembic migrations
    # But if you don't want to use migrations, create
    # the tables un-commenting the next lines
    # from sqlmodel import SQLModel

    # This works because the models are already imported and registered from app.models
    # SQLModel.metadata.create_all(engine)

    # Initialize RBAC system
    initialize_default_roles_and_permissions(session)

    user = session.exec(
        select(User).where(User.email == settings.FIRST_SUPERUSER)
    ).first()
    if not user:
        user_in = UserCreate(
            email=settings.FIRST_SUPERUSER,
            password=settings.FIRST_SUPERUSER_PASSWORD,
            is_superuser=True,
        )
        user = crud.create_user(session=session, user_create=user_in)

        # Assign Admin role to the first superuser
        admin_role = session.exec(
            select(Role).where(Role.name == RoleType.ADMIN)
        ).first()
        if admin_role:
            try:
                assign_role_to_user(session, user.id, admin_role.id)
            except Exception:
                # Role already assigned or other error, continue
                pass
