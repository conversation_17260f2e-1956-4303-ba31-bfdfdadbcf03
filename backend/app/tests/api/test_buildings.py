import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.core.config import settings
from app.models import Building, School
from app.tests.utils.school import create_random_school
from app.tests.utils.utils import random_lower_string


def test_create_building(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    school = create_random_school(db)
    data = {
        "name": "Test Building",
        "school_id": str(school.id),
        "address": "123 Test St",
        "description": "Test Description",
    }
    response = client.post(
        f"{settings.API_V1_STR}/buildings/",
        headers=superuser_token_headers,
        json=data,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["school_id"] == data["school_id"]
    assert content["address"] == data["address"]
    assert content["description"] == data["description"]
    assert "id" in content
    assert "created_at" in content


def test_read_building(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    school = create_random_school(db)
    building = Building(
        name=random_lower_string(),
        school_id=school.id,
        address="123 Test St",
        description="Test Description",
    )
    db.add(building)
    db.commit()
    db.refresh(building)

    response = client.get(
        f"{settings.API_V1_STR}/buildings/{building.id}",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == building.name
    assert content["school_id"] == str(building.school_id)
    assert content["id"] == str(building.id)


def test_read_buildings(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    school = create_random_school(db)
    # Create multiple buildings
    for _ in range(3):
        building = Building(
            name=random_lower_string(),
            school_id=school.id,
        )
        db.add(building)
    db.commit()

    response = client.get(
        f"{settings.API_V1_STR}/buildings/",
        headers=superuser_token_headers,
        params={"school_id": str(school.id)},
    )
    assert response.status_code == 200
    content = response.json()
    assert len(content["data"]) >= 3
    assert content["count"] >= 3


def test_update_building(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    school = create_random_school(db)
    building = Building(
        name=random_lower_string(),
        school_id=school.id,
        address="123 Test St",
    )
    db.add(building)
    db.commit()
    db.refresh(building)

    data = {
        "name": "Updated Building Name",
        "address": "456 New St",
        "description": "Updated Description",
    }
    response = client.patch(
        f"{settings.API_V1_STR}/buildings/{building.id}",
        headers=superuser_token_headers,
        json=data,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["address"] == data["address"]
    assert content["description"] == data["description"]


def test_delete_building(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    school = create_random_school(db)
    building = Building(
        name=random_lower_string(),
        school_id=school.id,
    )
    db.add(building)
    db.commit()
    db.refresh(building)

    response = client.delete(
        f"{settings.API_V1_STR}/buildings/{building.id}",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["message"] == "Building deleted successfully"

    # Verify soft delete
    db.refresh(building)
    assert building.deleted_at is not None


def test_create_building_not_found_school(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    data = {
        "name": "Test Building",
        "school_id": "00000000-0000-0000-0000-000000000000",
        "address": "123 Test St",
    }
    response = client.post(
        f"{settings.API_V1_STR}/buildings/",
        headers=superuser_token_headers,
        json=data,
    )
    assert response.status_code == 404


def test_read_building_not_found(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    response = client.get(
        f"{settings.API_V1_STR}/buildings/00000000-0000-0000-0000-000000000000",
        headers=superuser_token_headers,
    )
    assert response.status_code == 404


def test_update_building_not_found(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    data = {"name": "Updated Name"}
    response = client.patch(
        f"{settings.API_V1_STR}/buildings/00000000-0000-0000-0000-000000000000",
        headers=superuser_token_headers,
        json=data,
    )
    assert response.status_code == 404


def test_delete_building_not_found(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    response = client.delete(
        f"{settings.API_V1_STR}/buildings/00000000-0000-0000-0000-000000000000",
        headers=superuser_token_headers,
    )
    assert response.status_code == 404
