#!/usr/bin/env python3
"""
Test script for the YOLOv8 threat detection system.
This script demonstrates the threat detection functionality without requiring a camera.
"""

import sys
import os
import numpy as np
import cv2
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.threat_detection import threat_detection_service

def create_test_image():
    """Create a simple test image with some shapes"""
    # Create a blank image
    img = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Add some background color
    img[:] = (50, 50, 50)
    
    # Add some text to simulate objects
    cv2.putText(img, "TEST IMAGE", (200, 100), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    cv2.putText(img, "No real objects detected", (150, 200), cv2.FONT_HERSHEY_SIMPLEX, 1, (200, 200, 200), 2)
    cv2.putText(img, "Use real camera for actual detection", (100, 300), cv2.FONT_HERSHEY_SIMPLEX, 1, (200, 200, 200), 2)
    
    # Add some geometric shapes
    cv2.rectangle(img, (50, 350), (150, 450), (0, 255, 0), 2)
    cv2.circle(img, (300, 400), 50, (0, 0, 255), 2)
    cv2.ellipse(img, (500, 400), (80, 40), 0, 0, 360, (255, 0, 0), 2)
    
    return img

def test_threat_detection_service():
    """Test the threat detection service with a sample image"""
    print("🔍 Testing YOLOv8 Threat Detection Service")
    print("=" * 50)
    
    try:
        # Create a test image
        test_image = create_test_image()
        print("✅ Test image created successfully")
        
        # Test object detection
        print("\n🔍 Running object detection...")
        annotated_frame, detections = threat_detection_service.detect_objects(test_image)
        
        print(f"✅ Detection completed. Found {len(detections)} objects")
        
        # Display detection results
        if detections:
            print("\n📋 Detection Results:")
            print("-" * 30)
            for i, detection in enumerate(detections, 1):
                threat_status = "🚨 THREAT" if detection.get('is_threat', False) else "✅ Safe"
                print(f"{i}. {detection['class']} - Confidence: {detection['confidence']:.2f} - {threat_status}")
                print(f"   Bounding Box: {detection['bbox']}")
        else:
            print("ℹ️  No objects detected in test image (expected for synthetic image)")
        
        # Test threat detection logic
        print("\n🧪 Testing threat detection logic...")
        
        # Test with a simulated person detection (high threat)
        test_detection_person = {
            'class': 'person',
            'confidence': 0.85,
            'bbox': [100, 100, 200, 300],
            'is_threat': False,
            'class_id': 0
        }
        
        is_threat = threat_detection_service._is_threat('person', 0.85)
        print(f"Person detection (confidence 0.85): {'🚨 THREAT' if is_threat else '✅ Safe'}")
        
        # Test with a simulated knife detection (high threat)
        is_threat_knife = threat_detection_service._is_threat('knife', 0.75)
        print(f"Knife detection (confidence 0.75): {'🚨 THREAT' if is_threat_knife else '✅ Safe'}")
        
        # Test with a simulated car detection (not a threat)
        is_threat_car = threat_detection_service._is_threat('car', 0.90)
        print(f"Car detection (confidence 0.90): {'🚨 THREAT' if is_threat_car else '✅ Safe'}")
        
        # Test frame encoding
        print("\n📸 Testing frame encoding...")
        encoded_frame = threat_detection_service.encode_frame_to_base64(test_image)
        if encoded_frame:
            print(f"✅ Frame encoded successfully (length: {len(encoded_frame)} characters)")
        else:
            print("❌ Frame encoding failed")
        
        # Test threat alert printing
        print("\n🚨 Testing threat alert system...")
        threat_detections = [
            {
                'class': 'person',
                'confidence': 0.85,
                'bbox': [100, 100, 200, 300],
                'is_threat': True
            },
            {
                'class': 'knife',
                'confidence': 0.75,
                'bbox': [150, 150, 180, 200],
                'is_threat': True
            }
        ]
        
        threat_detection_service.print_threat_alert("test_camera", threat_detections)
        
        print("\n✅ All tests completed successfully!")
        print("\n📝 Summary:")
        print("- YOLOv8 model loaded and functional")
        print("- Object detection pipeline working")
        print("- Threat classification logic implemented")
        print("- Frame encoding functional")
        print("- Alert system operational")
        
        print("\n🚀 To start the full system:")
        print("1. Run: cd backend && uv run fastapi dev app/main.py")
        print("2. Open: http://localhost:8000/api/v1/threat-detection/interface/")
        print("3. Click 'Start Detection' to begin real-time monitoring")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("🛡️ YOLOv8 Threat Detection System - Test Suite")
    print("=" * 60)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = test_threat_detection_service()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! The threat detection system is ready to use.")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
    
    print(f"⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
