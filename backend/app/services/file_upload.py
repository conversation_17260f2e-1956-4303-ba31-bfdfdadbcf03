import os
import uuid
from pathlib import Path
from typing import Optional, <PERSON><PERSON>
import boto3
from botocore.exceptions import ClientError
from fastapi import UploadFile
from app.core.config import settings


class FileUploadService:
    def __init__(self):
        self.cloud_environment = os.getenv("CLOUD_ENVIRONMENT", "local")
        self.media_path = Path("media")

        if self.cloud_environment == "aws":
            self.s3_client = boto3.client(
                "s3",
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                region_name=os.getenv("AWS_REGION", "us-east-1")
            )
            self.s3_bucket = os.getenv("AWS_S3_BUCKET", "school-floor-plans")

    async def upload_floor_plan(self, file: UploadFile, floor_id: str) -> Optional[Tuple[str, str]]:
        """Upload floor plan image to local storage or S3 based on environment

        Returns:
            Tuple[base_path, file_path] or None if no file provided
            - base_path: S3 endpoint URL or local media folder path
            - file_path: file path with floor PK to avoid conflicts
        """
        if not file:
            return None

        # Generate unique filename with floor_id to avoid conflicts
        file_extension = file.filename.split(".")[-1] if "." in file.filename else "jpg"
        unique_filename = f"floor_plans/{floor_id}/{uuid.uuid4()}.{file_extension}"

        if self.cloud_environment == "aws":
            return await self._upload_to_s3(file, unique_filename)
        else:
            return await self._upload_to_local(file, unique_filename)
    
    async def _upload_to_local(self, file: UploadFile, filename: str) -> Tuple[str, str]:
        """Upload file to local media directory"""
        # Create media directory if it doesn't exist
        file_path = self.media_path / filename
        file_path.parent.mkdir(parents=True, exist_ok=True)

        # Save file
        content = await file.read()
        with open(file_path, "wb") as f:
            f.write(content)

        # Return base_path and file_path
        return "/media", filename

    async def _upload_to_s3(self, file: UploadFile, filename: str) -> Tuple[str, str]:
        """Upload file to AWS S3"""
        try:
            content = await file.read()
            self.s3_client.put_object(
                Bucket=self.s3_bucket,
                Key=filename,
                Body=content,
                ContentType=file.content_type or "image/jpeg"
            )

            # Return base_path (S3 endpoint) and file_path
            base_path = f"https://{self.s3_bucket}.s3.amazonaws.com"
            return base_path, filename
        except ClientError as e:
            print(f"Error uploading to S3: {e}")
            raise Exception("Failed to upload file to S3")
    
    async def delete_floor_plan(self, base_path: str, file_path: str) -> bool:
        """Delete floor plan image from storage"""
        if not base_path or not file_path:
            return True

        if self.cloud_environment == "aws":
            return await self._delete_from_s3(file_path)
        else:
            return await self._delete_from_local(file_path)

    async def _delete_from_local(self, file_path: str) -> bool:
        """Delete file from local storage"""
        try:
            # file_path already contains the relative path
            full_file_path = self.media_path / file_path

            if full_file_path.exists():
                full_file_path.unlink()
            return True
        except Exception as e:
            print(f"Error deleting local file: {e}")
            return False

    async def _delete_from_s3(self, file_path: str) -> bool:
        """Delete file from S3"""
        try:
            # file_path is the S3 key
            self.s3_client.delete_object(Bucket=self.s3_bucket, Key=file_path)
            return True
        except ClientError as e:
            print(f"Error deleting from S3: {e}")
            return False


# Singleton instance
file_upload_service = FileUploadService()
