import cv2
import numpy as np
import base64
import asyncio
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from ultralytics import <PERSON>OL<PERSON>
from sqlmodel import Session

from app.models import ThreatDetection, ThreatDetectionCreate
from app.core.db import engine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ThreatDetectionService:
    def __init__(self, model_path: str = 'yolov8n.pt'):
        """
        Initialize the threat detection service with YOLOv8 model
        
        Args:
            model_path: Path to the YOLOv8 model file
        """
        try:
            self.model = YOLO(model_path)
            logger.info(f"YOLOv8 model loaded successfully from {model_path}")
        except Exception as e:
            logger.error(f"Failed to load YOLOv8 model: {e}")
            raise
        
        # Define threat classes - these are objects that could be considered threats
        # Based on COCO dataset classes that YOLOv8 can detect
        self.threat_classes = {
            # 'person': 0,      # Person detection for unauthorized access
            'knife': 43,      # Sharp objects
            'scissors': 76,   # Sharp objects
            'bottle': 39,     # Potential weapons
            'baseball bat': 37,  # Potential weapons
            'sports ball': 32,   # Thrown objects
            'fire hydrant': 10,  # Obstruction
            'stop sign': 11,     # Vandalism target
        }
        
        # Confidence threshold for threat detection
        self.threat_confidence_threshold = 0.6
        
        # General detection confidence threshold
        self.detection_confidence_threshold = 0.5

    def detect_objects(self, frame: np.ndarray) -> tuple[np.ndarray, List[Dict[str, Any]]]:
        """
        Perform object detection on a frame using YOLOv8
        
        Args:
            frame: Input image frame
            
        Returns:
            Tuple of (annotated_frame, detections_list)
        """
        try:
            results = self.model(frame)
            
            detections = []
            annotated_frame = frame.copy()
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Get bounding box coordinates
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        # Get class name
                        class_name = self.model.names[class_id]
                        
                        # Filter by confidence threshold
                        if confidence > self.detection_confidence_threshold:
                            # Determine if this is a threat
                            is_threat = self._is_threat(class_name, confidence)
                            
                            # Choose color based on threat level
                            color = (0, 0, 255) if is_threat else (0, 255, 0)  # Red for threats, green for normal
                            
                            # Draw bounding box
                            cv2.rectangle(annotated_frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)
                            
                            # Draw label with threat indicator
                            threat_indicator = "⚠️ THREAT" if is_threat else ""
                            label = f"{class_name}: {confidence:.2f} {threat_indicator}"
                            cv2.putText(annotated_frame, label, (int(x1), int(y1-10)), 
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                            
                            # Store detection info
                            detection_data = {
                                'class': class_name,
                                'confidence': float(confidence),
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'is_threat': is_threat,
                                'class_id': class_id
                            }
                            detections.append(detection_data)
                            
                            # Log threat detection
                            if is_threat:
                                logger.warning(f"THREAT DETECTED: {class_name} with confidence {confidence:.2f}")
            
            return annotated_frame, detections
            
        except Exception as e:
            logger.error(f"Error in object detection: {e}")
            return frame, []

    def _is_threat(self, class_name: str, confidence: float) -> bool:
        """
        Determine if a detected object is considered a threat
        
        Args:
            class_name: Name of the detected class
            confidence: Detection confidence
            
        Returns:
            True if the object is considered a threat
        """
        # Check if the class is in our threat list and confidence is above threshold
        if class_name in self.threat_classes and confidence > self.threat_confidence_threshold:
            return True
        
        # Additional threat logic can be added here
        # For example, multiple people in restricted areas, etc.
        
        return False

    async def save_threat_detection(self, camera_id: str, detection: Dict[str, Any]) -> Optional[str]:
        """
        Save threat detection to database
        
        Args:
            camera_id: ID of the camera that detected the threat
            detection: Detection data dictionary
            
        Returns:
            Threat detection ID if saved successfully, None otherwise
        """
        try:
            if not detection.get('is_threat', False):
                return None
                
            threat_data = ThreatDetectionCreate(
                camera_id=camera_id,
                threat_type=detection['class'],
                confidence=detection['confidence'],
                bbox_x1=detection['bbox'][0],
                bbox_y1=detection['bbox'][1],
                bbox_x2=detection['bbox'][2],
                bbox_y2=detection['bbox'][3],
                is_active_threat=True
            )
            
            with Session(engine) as session:
                threat_detection = ThreatDetection.model_validate(threat_data)
                session.add(threat_detection)
                session.commit()
                session.refresh(threat_detection)
                
                logger.info(f"Threat detection saved with ID: {threat_detection.id}")
                return str(threat_detection.id)
                
        except Exception as e:
            logger.error(f"Error saving threat detection: {e}")
            return None

    def encode_frame_to_base64(self, frame: np.ndarray) -> str:
        """
        Encode frame to base64 string for transmission
        
        Args:
            frame: Image frame
            
        Returns:
            Base64 encoded string
        """
        try:
            _, buffer = cv2.imencode('.jpg', frame)
            frame_base64 = base64.b64encode(buffer).decode('utf-8')
            return frame_base64
        except Exception as e:
            logger.error(f"Error encoding frame: {e}")
            return ""

    def print_threat_alert(self, camera_id: str, detections: List[Dict[str, Any]]) -> None:
        """
        Print threat alert message to console
        
        Args:
            camera_id: ID of the camera
            detections: List of detections
        """
        threats = [d for d in detections if d.get('is_threat', False)]
        
        if threats:
            print("\n" + "="*50)
            print("🚨 THREAT ALERT 🚨")
            print(f"Camera ID: {camera_id}")
            print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 50)
            
            for threat in threats:
                print(f"Threat Type: {threat['class']}")
                print(f"Confidence: {threat['confidence']:.2f}")
                print(f"Location: {threat['bbox']}")
                print("-" * 30)
            
            print("="*50 + "\n")

# Global instance
threat_detection_service = ThreatDetectionService()
