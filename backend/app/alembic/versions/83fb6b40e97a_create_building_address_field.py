"""create building address field

Revision ID: 83fb6b40e97a
Revises: add_soft_delete_001
Create Date: 2025-06-10 17:02:57.040304

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '83fb6b40e97a'
down_revision = 'add_soft_delete_001'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('building', sa.Column('address', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    op.add_column('building', sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True))
    op.add_column('camera', sa.Column('is_deleted', sa.<PERSON>(), nullable=False))
    op.add_column('camera', sa.<PERSON>umn('deleted_at', sa.DateTime(), nullable=True))
    op.create_index(op.f('ix_camera_is_deleted'), 'camera', ['is_deleted'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_camera_is_deleted'), table_name='camera')
    op.drop_column('camera', 'deleted_at')
    op.drop_column('camera', 'is_deleted')
    op.drop_column('building', 'description')
    op.drop_column('building', 'address')
    # ### end Alembic commands ###
