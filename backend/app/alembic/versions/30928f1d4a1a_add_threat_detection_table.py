"""Add threat detection table

Revision ID: 30928f1d4a1a
Revises: a0707f25b2da
Create Date: 2025-05-23 11:54:53.801989

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '30928f1d4a1a'
down_revision = 'a0707f25b2da'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('threatdetection',
    sa.Column('camera_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('threat_type', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('confidence', sa.Float(), nullable=False),
    sa.Column('bbox_x1', sa.Integer(), nullable=False),
    sa.Column('bbox_y1', sa.<PERSON>ger(), nullable=False),
    sa.Column('bbox_x2', sa.Integer(), nullable=False),
    sa.Column('bbox_y2', sa.Integer(), nullable=False),
    sa.Column('image_path', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('is_active_threat', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('detected_at', sa.DateTime(), nullable=False),
    sa.Column('resolved_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('threatdetection')
    # ### end Alembic commands ###
