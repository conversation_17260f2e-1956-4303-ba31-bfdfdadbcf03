"""Fix UserRole relationship foreign keys

Revision ID: ab09be3fb4aa
Revises: cb990aa865f1
Create Date: 2025-05-26 18:48:26.104097

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'ab09be3fb4aa'
down_revision = 'cb990aa865f1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
