"""Add building floor and camera models

Revision ID: 16782b5279a0
Revises: ab09be3fb4aa
Create Date: 2025-06-10 15:33:38.049791

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '16782b5279a0'
down_revision = 'ab09be3fb4aa'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('building',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('school_id', sa.Uuid(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['school_id'], ['school.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('floor',
    sa.Column('number', sa.Integer(), nullable=False),
    sa.Column('building_id', sa.Uuid(), nullable=False),
    sa.Column('floor_plan_image', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['building_id'], ['building.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('camera',
    sa.Column('position', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('floor_id', sa.Uuid(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['floor_id'], ['floor.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('camera')
    op.drop_table('floor')
    op.drop_table('building')
    # ### end Alembic commands ###
