"""Add soft delete to school building floor

Revision ID: add_soft_delete_001
Revises: 16782b5279a0
Create Date: 2025-01-10 16:45:00.000000

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = 'add_soft_delete_001'
down_revision = '16782b5279a0'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add soft delete columns to school table
    op.add_column('school', sa.Column('is_deleted', sa.<PERSON>(), nullable=False, server_default='false'))
    op.add_column('school', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.create_index(op.f('ix_school_is_deleted'), 'school', ['is_deleted'], unique=False)
    
    # Add soft delete columns to building table
    op.add_column('building', sa.Column('is_deleted', sa.<PERSON>(), nullable=False, server_default='false'))
    op.add_column('building', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.create_index(op.f('ix_building_is_deleted'), 'building', ['is_deleted'], unique=False)
    
    # Add soft delete columns to floor table (if not already present)
    # Check if columns already exist before adding
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    floor_columns = [col['name'] for col in inspector.get_columns('floor')]
    
    if 'is_deleted' not in floor_columns:
        op.add_column('floor', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'))
        op.create_index(op.f('ix_floor_is_deleted'), 'floor', ['is_deleted'], unique=False)
    
    if 'deleted_at' not in floor_columns:
        op.add_column('floor', sa.Column('deleted_at', sa.DateTime(), nullable=True))


def downgrade() -> None:
    # Remove soft delete columns from floor table
    op.drop_index(op.f('ix_floor_is_deleted'), table_name='floor')
    op.drop_column('floor', 'deleted_at')
    op.drop_column('floor', 'is_deleted')
    
    # Remove soft delete columns from building table
    op.drop_index(op.f('ix_building_is_deleted'), table_name='building')
    op.drop_column('building', 'deleted_at')
    op.drop_column('building', 'is_deleted')
    
    # Remove soft delete columns from school table
    op.drop_index(op.f('ix_school_is_deleted'), table_name='school')
    op.drop_column('school', 'deleted_at')
    op.drop_column('school', 'is_deleted')
