"""
Example script demonstrating RBAC usage.
This script shows how to:
1. Create users with different roles
2. Check permissions
3. Use role-based access control in API endpoints
"""

import uuid
from sqlmodel import Session

from app.core.db import engine
from app.core.rbac import (
    assign_role_to_user, user_has_permission, user_has_role,
    get_user_permissions, get_user_roles
)
from app.models import User, Role, RoleType, PermissionType
from app import crud
from app.models import UserCreate


def create_example_users(session: Session) -> dict:
    """Create example users for different roles."""
    users = {}
    
    # Create a teacher
    teacher_user = crud.create_user(
        session=session,
        user_create=UserCreate(
            email="<EMAIL>",
            password="teacher123",
            full_name="<PERSON>"
        )
    )
    
    # Create a security officer
    security_user = crud.create_user(
        session=session,
        user_create=UserCreate(
            email="<EMAIL>",
            password="security123",
            full_name="Jane Security"
        )
    )
    
    # Create an IT admin
    it_user = crud.create_user(
        session=session,
        user_create=UserCreate(
            email="<EMAIL>",
            password="itadmin123",
            full_name="<PERSON> ITAdmin"
        )
    )
    
    users['teacher'] = teacher_user
    users['security'] = security_user
    users['it'] = it_user
    
    return users


def assign_roles_to_users(session: Session, users: dict) -> None:
    """Assign appropriate roles to users."""
    
    # Get roles
    teacher_role = session.query(Role).filter(Role.name == RoleType.TEACHERS).first()
    security_role = session.query(Role).filter(Role.name == RoleType.SECURITY).first()
    it_role = session.query(Role).filter(Role.name == RoleType.IT).first()
    
    # Assign roles
    if teacher_role:
        assign_role_to_user(session, users['teacher'].id, teacher_role.id)
        print(f"Assigned TEACHERS role to {users['teacher'].email}")
    
    if security_role:
        assign_role_to_user(session, users['security'].id, security_role.id)
        print(f"Assigned SECURITY role to {users['security'].email}")
    
    if it_role:
        assign_role_to_user(session, users['it'].id, it_role.id)
        print(f"Assigned IT role to {users['it'].email}")


def demonstrate_permission_checks(session: Session, users: dict) -> None:
    """Demonstrate permission checking."""
    
    print("\n=== Permission Checks ===")
    
    # Check teacher permissions
    teacher_id = users['teacher'].id
    print(f"\nTeacher ({users['teacher'].email}) permissions:")
    print(f"- Can manage classes: {user_has_permission(session, teacher_id, PermissionType.MANAGE_CLASSES)}")
    print(f"- Can view threats: {user_has_permission(session, teacher_id, PermissionType.VIEW_THREATS)}")
    print(f"- Can configure cameras: {user_has_permission(session, teacher_id, PermissionType.CONFIGURE_CAMERAS)}")
    print(f"- Can delete users: {user_has_permission(session, teacher_id, PermissionType.DELETE_USER)}")
    
    # Check security permissions
    security_id = users['security'].id
    print(f"\nSecurity Officer ({users['security'].email}) permissions:")
    print(f"- Can view threats: {user_has_permission(session, security_id, PermissionType.VIEW_THREATS)}")
    print(f"- Can manage threats: {user_has_permission(session, security_id, PermissionType.MANAGE_THREATS)}")
    print(f"- Can configure cameras: {user_has_permission(session, security_id, PermissionType.CONFIGURE_CAMERAS)}")
    print(f"- Can manage classes: {user_has_permission(session, security_id, PermissionType.MANAGE_CLASSES)}")
    
    # Check IT permissions
    it_id = users['it'].id
    print(f"\nIT Admin ({users['it'].email}) permissions:")
    print(f"- Can configure system: {user_has_permission(session, it_id, PermissionType.SYSTEM_CONFIG)}")
    print(f"- Can view logs: {user_has_permission(session, it_id, PermissionType.VIEW_LOGS)}")
    print(f"- Can update users: {user_has_permission(session, it_id, PermissionType.UPDATE_USER)}")
    print(f"- Can manage roles: {user_has_permission(session, it_id, PermissionType.MANAGE_ROLES)}")


def demonstrate_role_checks(session: Session, users: dict) -> None:
    """Demonstrate role checking."""
    
    print("\n=== Role Checks ===")
    
    for user_type, user in users.items():
        print(f"\n{user_type.title()} ({user.email}) roles:")
        user_roles = get_user_roles(session, user.id)
        for role in user_roles:
            print(f"- {role.name.value}")
        
        print(f"Has ADMIN role: {user_has_role(session, user.id, RoleType.ADMIN)}")
        print(f"Has TEACHERS role: {user_has_role(session, user.id, RoleType.TEACHERS)}")
        print(f"Has SECURITY role: {user_has_role(session, user.id, RoleType.SECURITY)}")


def main():
    """Main demonstration function."""
    print("=== RBAC System Demonstration ===")
    
    with Session(engine) as session:
        # Create example users
        print("\n1. Creating example users...")
        users = create_example_users(session)
        
        # Assign roles
        print("\n2. Assigning roles to users...")
        assign_roles_to_users(session, users)
        
        # Demonstrate permission checks
        demonstrate_permission_checks(session, users)
        
        # Demonstrate role checks
        demonstrate_role_checks(session, users)
        
        print("\n=== Demonstration Complete ===")
        print("\nTo use RBAC in your API endpoints:")
        print("1. Use @Depends(require_permission(PermissionType.SOME_PERMISSION))")
        print("2. Use @Depends(require_role(RoleType.SOME_ROLE))")
        print("3. Use @Depends(require_any_role([RoleType.ROLE1, RoleType.ROLE2]))")


if __name__ == "__main__":
    main()
