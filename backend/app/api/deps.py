from collections.abc import Generator
from typing import Annotated

import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jwt.exceptions import InvalidTokenError
from pydantic import ValidationError
from sqlmodel import Session

from app.core import security
from app.core.config import settings
from app.core.db import engine
from app.core.rbac import (
    get_user_roles, get_user_permissions, user_has_permission,
    user_has_role, user_has_any_role
)
from app.models import TokenPayload, User, RoleType, PermissionType, Role

reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/login/access-token"
)


def get_db() -> Generator[Session, None, None]:
    with Session(engine) as session:
        yield session


SessionDep = Annotated[Session, Depends(get_db)]
TokenDep = Annotated[str, Depends(reusable_oauth2)]


def get_current_user(session: SessionDep, token: TokenDep) -> User:
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[security.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (InvalidTokenError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
    user = session.get(User, token_data.sub)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return user


CurrentUser = Annotated[User, Depends(get_current_user)]


def get_current_active_superuser(current_user: CurrentUser) -> User:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403, detail="The user doesn't have enough privileges"
        )
    return current_user


# RBAC Dependencies
def require_permission(permission: PermissionType):
    """Dependency factory to require a specific permission."""
    def check_permission(session: SessionDep, current_user: CurrentUser) -> User:
        if current_user.is_superuser:
            return current_user

        if not user_has_permission(session, current_user.id, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission.value}' required"
            )
        return current_user
    return check_permission


def require_role(role: RoleType):
    """Dependency factory to require a specific role."""
    def check_role(session: SessionDep, current_user: CurrentUser) -> User:
        if current_user.is_superuser:
            return current_user

        if not user_has_role(session, current_user.id, role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role '{role.value}' required"
            )
        return current_user
    return check_role


def require_any_role(roles: list[RoleType]):
    """Dependency factory to require any of the specified roles."""
    def check_roles(session: SessionDep, current_user: CurrentUser) -> User:
        if current_user.is_superuser:
            return current_user

        if not user_has_any_role(session, current_user.id, roles):
            role_names = [role.value for role in roles]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"One of these roles required: {', '.join(role_names)}"
            )
        return current_user
    return check_roles


def get_current_user_with_roles(session: SessionDep, token: TokenDep) -> User:
    """Get current user with their roles loaded."""
    user = get_current_user(session, token)
    # Load user roles
    user_roles = get_user_roles(session, user.id)
    # This is a bit of a hack to add roles to the user object for API responses
    user.roles = user_roles  # type: ignore
    return user


CurrentUserWithRoles = Annotated[User, Depends(get_current_user_with_roles)]


# RBAC Annotated Dependencies
def RequirePermission(permission: PermissionType):
    """Create an annotated dependency for a specific permission."""
    return Annotated[User, Depends(require_permission(permission))]


def RequireRole(role: RoleType):
    """Create an annotated dependency for a specific role."""
    return Annotated[User, Depends(require_role(role))]


def RequireAnyRole(roles: list[RoleType]):
    """Create an annotated dependency for any of the specified roles."""
    return Annotated[User, Depends(require_any_role(roles))]
