import uuid
from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import func, select

from app.api.deps import CurrentUser, SessionDep, get_current_active_superuser
from app.models import School, SchoolCreate, SchoolPublic, SchoolsPublic, SchoolUpdate, Message
from datetime import datetime

router = APIRouter(prefix="/schools", tags=["schools"])


@router.get("/", response_model=SchoolsPublic)
def read_schools(
    session: SessionDep, 
    current_user: CurrentUser,
    skip: int = 0, 
    limit: int = 100,
    include_deleted: bool = False
) -> Any:
    """
    Retrieve schools.
    """
    query = select(School)
    count_query = select(func.count()).select_from(School)
    
    if not include_deleted:
        query = query.where(School.is_deleted == False)
        count_query = count_query.where(School.is_deleted == False)
    
    count = session.exec(count_query).one()
    schools = session.exec(query.offset(skip).limit(limit)).all()
    
    return SchoolsPublic(data=schools, count=count)


@router.get("/{school_id}", response_model=SchoolPublic)
def read_school(
    school_id: uuid.UUID, 
    session: SessionDep, 
    current_user: CurrentUser
) -> Any:
    """
    Get school by ID.
    """
    school = session.get(School, school_id)
    if not school or school.is_deleted:
        raise HTTPException(status_code=404, detail="School not found")
    return school


@router.post("/", response_model=SchoolPublic)
def create_school(
    *, 
    session: SessionDep, 
    current_user: CurrentUser, 
    school_in: SchoolCreate
) -> Any:
    """
    Create new school.
    """
    school = School.model_validate(school_in)
    session.add(school)
    session.commit()
    session.refresh(school)
    return school


@router.patch("/{school_id}", response_model=SchoolPublic)
def update_school(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    school_id: uuid.UUID,
    school_in: SchoolUpdate,
) -> Any:
    """
    Update a school.
    """
    school = session.get(School, school_id)
    if not school or school.is_deleted:
        raise HTTPException(status_code=404, detail="School not found")
    
    update_dict = school_in.model_dump(exclude_unset=True)
    if update_dict:  # Only update timestamp if there are changes
        update_dict["updated_at"] = datetime.utcnow()
    
    school.sqlmodel_update(update_dict)
    session.add(school)
    session.commit()
    session.refresh(school)
    return school


@router.delete("/{school_id}")
def delete_school(
    session: SessionDep, 
    current_user: CurrentUser, 
    school_id: uuid.UUID
) -> Message:
    """
    Soft delete a school.
    """
    school = session.get(School, school_id)
    if not school or school.is_deleted:
        raise HTTPException(status_code=404, detail="School not found")
    
    # Soft delete the school
    school.is_deleted = True
    school.deleted_at = datetime.utcnow()
    
    # Soft delete all buildings in the school
    for building in school.buildings:
        building.is_deleted = True
        building.deleted_at = datetime.utcnow()
        
        # Soft delete all floors in the building
        for floor in building.floors:
            floor.is_deleted = True
            floor.deleted_at = datetime.utcnow()
            
            # Soft delete all cameras on the floor
            for camera in floor.cameras:
                camera.is_deleted = True
                camera.deleted_at = datetime.utcnow()
    
    session.add(school)
    session.commit()
    return Message(message="School deleted successfully")
