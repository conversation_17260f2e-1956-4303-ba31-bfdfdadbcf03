import uuid
from typing import Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, UploadFile, File
from sqlmodel import func, select

from app.api.deps import CurrentUser, SessionDep
from app.models import Floor, FloorCreate, FloorPublic, FloorsPublic, FloorUpdate, Message, Building
from app.services.file_upload import file_upload_service

router = APIRouter(prefix="/floors", tags=["floors"])


@router.get("/", response_model=FloorsPublic)
def read_floors(
    session: SessionDep, 
    current_user: CurrentUser,
    skip: int = 0, 
    limit: int = 100,
    building_id: uuid.UUID | None = None,
    include_deleted: bool = False
) -> Any:
    """
    Retrieve floors.
    """
    query = select(Floor)
    count_query = select(func.count()).select_from(Floor)
    
    if not include_deleted:
        query = query.where(Floor.is_deleted == False)
        count_query = count_query.where(Floor.is_deleted == False)
    
    if building_id:
        query = query.where(Floor.building_id == building_id)
        count_query = count_query.where(Floor.building_id == building_id)
    
    count = session.exec(count_query).one()
    floors = session.exec(query.offset(skip).limit(limit)).all()
    
    return FloorsPublic(data=floors, count=count)


@router.get("/{floor_id}", response_model=FloorPublic)
def read_floor(
    floor_id: uuid.UUID, 
    session: SessionDep, 
    current_user: CurrentUser
) -> Any:
    """
    Get floor by ID.
    """
    floor = session.get(Floor, floor_id)
    if not floor or floor.is_deleted:
        raise HTTPException(status_code=404, detail="Floor not found")
    return floor


@router.post("/", response_model=FloorPublic)
def create_floor(
    *, 
    session: SessionDep, 
    current_user: CurrentUser, 
    floor_in: FloorCreate
) -> Any:
    """
    Create new floor.
    """
    # Verify building exists and is not deleted
    building = session.get(Building, floor_in.building_id)
    if not building or building.is_deleted:
        raise HTTPException(status_code=404, detail="Building not found")
    
    floor = Floor.model_validate(floor_in)
    session.add(floor)
    session.commit()
    session.refresh(floor)
    return floor


@router.patch("/{floor_id}", response_model=FloorPublic)
def update_floor(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    floor_id: uuid.UUID,
    floor_in: FloorUpdate,
) -> Any:
    """
    Update a floor.
    """
    floor = session.get(Floor, floor_id)
    if not floor or floor.is_deleted:
        raise HTTPException(status_code=404, detail="Floor not found")
    
    update_dict = floor_in.model_dump(exclude_unset=True)
    if update_dict:  # Only update timestamp if there are changes
        update_dict["updated_at"] = datetime.utcnow()
    
    floor.sqlmodel_update(update_dict)
    session.add(floor)
    session.commit()
    session.refresh(floor)
    return floor


@router.delete("/{floor_id}")
def delete_floor(
    session: SessionDep, 
    current_user: CurrentUser, 
    floor_id: uuid.UUID
) -> Message:
    """
    Soft delete a floor.
    """
    floor = session.get(Floor, floor_id)
    if not floor or floor.is_deleted:
        raise HTTPException(status_code=404, detail="Floor not found")
    
    # Soft delete the floor
    floor.is_deleted = True
    floor.deleted_at = datetime.utcnow()
    
    # Soft delete all cameras on the floor
    for camera in floor.cameras:
        camera.is_deleted = True
        camera.deleted_at = datetime.utcnow()
    
    session.add(floor)
    session.commit()
    return Message(message="Floor deleted successfully")


@router.post("/{floor_id}/upload-floor-plan", response_model=FloorPublic)
async def upload_floor_plan(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    floor_id: uuid.UUID,
    file: UploadFile = File(...),
) -> Any:
    """
    Upload floor plan image for a floor.
    """
    floor = session.get(Floor, floor_id)
    if not floor or floor.is_deleted:
        raise HTTPException(status_code=404, detail="Floor not found")

    # Upload the file and get base_path and file_path
    upload_result = await file_upload_service.upload_floor_plan(file, str(floor_id))
    if not upload_result:
        raise HTTPException(status_code=400, detail="Failed to upload file")

    base_path, file_path = upload_result

    # Update floor with new paths
    floor.base_path = base_path
    floor.file_path = file_path
    floor.updated_at = datetime.utcnow()

    session.add(floor)
    session.commit()
    session.refresh(floor)
    return floor
