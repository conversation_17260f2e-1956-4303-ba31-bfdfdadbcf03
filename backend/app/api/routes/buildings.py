import uuid
from typing import Any
from datetime import datetime

from fastapi import APIRouter, HTTPException
from sqlmodel import func, select

from app.api.deps import CurrentUser, SessionDep
from app.models import Building, BuildingCreate, BuildingPublic, BuildingsPublic, BuildingUpdate, Message

router = APIRouter(prefix="/buildings", tags=["buildings"])


@router.get("/", response_model=BuildingsPublic)
def read_buildings(
    session: SessionDep, 
    current_user: CurrentUser,
    skip: int = 0, 
    limit: int = 100,
    include_deleted: bool = False,
    school_id: uuid.UUID | None = None
) -> Any:
    """
    Retrieve buildings.
    """
    query = select(Building)
    count_query = select(func.count()).select_from(Building)
    
    if not include_deleted:
        query = query.where(Building.is_deleted == False)
        count_query = count_query.where(Building.is_deleted == False)
    
    if school_id:
        query = query.where(Building.school_id == school_id)
        count_query = count_query.where(Building.school_id == school_id)
    
    count = session.exec(count_query).one()
    buildings = session.exec(query.offset(skip).limit(limit)).all()
    
    return BuildingsPublic(data=buildings, count=count)


@router.get("/{building_id}", response_model=BuildingPublic)
def read_building(
    building_id: uuid.UUID, 
    session: SessionDep, 
    current_user: CurrentUser
) -> Any:
    """
    Get building by ID.
    """
    building = session.get(Building, building_id)
    if not building or building.is_deleted:
        raise HTTPException(status_code=404, detail="Building not found")
    return building


@router.post("/", response_model=BuildingPublic)
def create_building(
    *, 
    session: SessionDep, 
    current_user: CurrentUser, 
    building_in: BuildingCreate
) -> Any:
    """
    Create new building.
    """
    # Verify school exists and is not deleted
    from app.models import School
    school = session.get(School, building_in.school_id)
    if not school or school.is_deleted:
        raise HTTPException(status_code=404, detail="School not found")
    
    building = Building.model_validate(building_in)
    session.add(building)
    session.commit()
    session.refresh(building)
    return building


@router.patch("/{building_id}", response_model=BuildingPublic)
def update_building(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    building_id: uuid.UUID,
    building_in: BuildingUpdate,
) -> Any:
    """
    Update a building.
    """
    building = session.get(Building, building_id)
    if not building or building.is_deleted:
        raise HTTPException(status_code=404, detail="Building not found")
    
    update_dict = building_in.model_dump(exclude_unset=True)
    if update_dict:  # Only update timestamp if there are changes
        update_dict["updated_at"] = datetime.utcnow()
    
    building.sqlmodel_update(update_dict)
    session.add(building)
    session.commit()
    session.refresh(building)
    return building


@router.delete("/{building_id}")
def delete_building(
    session: SessionDep, 
    current_user: CurrentUser, 
    building_id: uuid.UUID
) -> Message:
    """
    Soft delete a building.
    """
    building = session.get(Building, building_id)
    if not building or building.is_deleted:
        raise HTTPException(status_code=404, detail="Building not found")
    
    # Soft delete the building
    building.is_deleted = True
    building.deleted_at = datetime.utcnow()
    
    # Soft delete all floors in the building
    for floor in building.floors:
        floor.is_deleted = True
        floor.deleted_at = datetime.utcnow()
        
        # Soft delete all cameras on the floor
        for camera in floor.cameras:
            camera.is_deleted = True
            camera.deleted_at = datetime.utcnow()
    
    session.add(building)
    session.commit()
    return Message(message="Building deleted successfully")
