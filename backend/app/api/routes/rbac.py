"""
RBAC (Role-Based Access Control) API routes.
"""

import uuid
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import select

from app.api.deps import (
    SessionDep, CurrentUser, RequirePermission, RequireRole
)
from app.core.rbac import (
    assign_role_to_user, remove_role_from_user,
    assign_permission_to_role, remove_permission_from_role,
    get_user_roles, get_user_permissions, get_role_permissions,
    RBACError, initialize_default_roles_and_permissions
)
from app.models import (
    Role, Permission, User, UserRole, RolePermission,
    RoleType, PermissionType, RoleCreate, RoleUpdate, RolePublic, RolesPublic,
    PermissionCreate, PermissionUpdate, PermissionPublic, PermissionsPublic,
    UserRoleAssign, UserRoleRemove, RolePermissionAssign, RolePermissionRemove,
    UserPublic, UsersPublic, Message
)

router = APIRouter(prefix="/rbac", tags=["rbac"])


# Role management endpoints
@router.get("/roles/", response_model=RolesPublic)
def read_roles(
    session: SessionDep,
    skip: int = 0,
    limit: int = 100,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
) -> Any:
    """
    Retrieve roles.
    """
    count_statement = select(Role)
    count = len(list(session.exec(count_statement)))

    statement = select(Role).offset(skip).limit(limit)
    roles = session.exec(statement).all()

    return RolesPublic(data=roles, count=count)


@router.post("/roles/", response_model=RolePublic)
def create_role(
    *,
    session: SessionDep,
    role_in: RoleCreate,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
) -> Any:
    """
    Create new role.
    """
    # Check if role already exists
    existing_role = session.exec(select(Role).where(Role.name == role_in.name)).first()
    if existing_role:
        raise HTTPException(
            status_code=400,
            detail="Role with this name already exists"
        )

    role = Role.model_validate(role_in)
    session.add(role)
    session.commit()
    session.refresh(role)
    return role


@router.get("/roles/{role_id}", response_model=RolePublic)
def read_role(
    role_id: uuid.UUID,
    session: SessionDep,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
) -> Any:
    """
    Get role by ID.
    """
    role = session.get(Role, role_id)
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")
    return role


@router.patch("/roles/{role_id}", response_model=RolePublic)
def update_role(
    *,
    session: SessionDep,
    role_id: uuid.UUID,
    role_in: RoleUpdate,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
) -> Any:
    """
    Update role.
    """
    role = session.get(Role, role_id)
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")

    update_dict = role_in.model_dump(exclude_unset=True)
    role.sqlmodel_update(update_dict)
    session.add(role)
    session.commit()
    session.refresh(role)
    return role


@router.delete("/roles/{role_id}")
def delete_role(
    role_id: uuid.UUID,
    session: SessionDep,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
) -> Message:
    """
    Delete role.
    """
    role = session.get(Role, role_id)
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")

    session.delete(role)
    session.commit()
    return Message(message="Role deleted successfully")


# Permission management endpoints
@router.get("/permissions/", response_model=PermissionsPublic)
def read_permissions(
    session: SessionDep,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Retrieve permissions.
    """
    count_statement = select(Permission)
    count = len(list(session.exec(count_statement)))

    statement = select(Permission).offset(skip).limit(limit)
    permissions = session.exec(statement).all()

    return PermissionsPublic(data=permissions, count=count)


@router.post("/permissions/", response_model=PermissionPublic)
def create_permission(
    *,
    session: SessionDep,
    permission_in: PermissionCreate,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
) -> Any:
    """
    Create new permission.
    """
    # Check if permission already exists
    existing_permission = session.exec(
        select(Permission).where(Permission.name == permission_in.name)
    ).first()
    if existing_permission:
        raise HTTPException(
            status_code=400,
            detail="Permission with this name already exists"
        )

    permission = Permission.model_validate(permission_in)
    session.add(permission)
    session.commit()
    session.refresh(permission)
    return permission


# User role assignment endpoints
@router.post("/users/{user_id}/roles")
def assign_user_role(
    *,
    session: SessionDep,
    user_id: uuid.UUID,
    assignment: UserRoleAssign,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
) -> Message:
    """
    Assign role to user.
    """
    # Verify user exists
    user = session.get(User, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Verify role exists
    role = session.get(Role, assignment.role_id)
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")

    try:
        assign_role_to_user(session, user_id, assignment.role_id, current_user.id)
        return Message(message="Role assigned successfully")
    except RBACError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/users/{user_id}/roles/{role_id}")
def remove_user_role(
    *,
    session: SessionDep,
    user_id: uuid.UUID,
    role_id: uuid.UUID,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
) -> Message:
    """
    Remove role from user.
    """
    success = remove_role_from_user(session, user_id, role_id)
    if not success:
        raise HTTPException(status_code=404, detail="User role assignment not found")

    return Message(message="Role removed successfully")


@router.get("/users/{user_id}/roles", response_model=RolesPublic)
def get_user_roles_endpoint(
    user_id: uuid.UUID,
    session: SessionDep,
    current_user: RequirePermission(PermissionType.READ_USER) = None,
) -> Any:
    """
    Get user's roles.
    """
    # Check if user exists
    user = session.get(User, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    roles = get_user_roles(session, user_id)
    return RolesPublic(data=roles, count=len(roles))


@router.get("/users/{user_id}/permissions")
def get_user_permissions_endpoint(
    user_id: uuid.UUID,
    session: SessionDep,
    current_user: RequirePermission(PermissionType.READ_USER) = None,
) -> Any:
    """
    Get user's permissions.
    """
    # Check if user exists
    user = session.get(User, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    permissions = get_user_permissions(session, user_id)
    return {"permissions": list(permissions)}


# Role permission assignment endpoints
@router.post("/roles/{role_id}/permissions")
def assign_role_permission(
    *,
    session: SessionDep,
    role_id: uuid.UUID,
    assignment: RolePermissionAssign,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
) -> Message:
    """
    Assign permission to role.
    """
    # Verify role exists
    role = session.get(Role, role_id)
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")

    # Verify permission exists
    permission = session.get(Permission, assignment.permission_id)
    if not permission:
        raise HTTPException(status_code=404, detail="Permission not found")

    try:
        assign_permission_to_role(session, role_id, assignment.permission_id)
        return Message(message="Permission assigned successfully")
    except RBACError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/roles/{role_id}/permissions/{permission_id}")
def remove_role_permission(
    *,
    session: SessionDep,
    role_id: uuid.UUID,
    permission_id: uuid.UUID,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
) -> Message:
    """
    Remove permission from role.
    """
    success = remove_permission_from_role(session, role_id, permission_id)
    if not success:
        raise HTTPException(status_code=404, detail="Role permission assignment not found")

    return Message(message="Permission removed successfully")


@router.get("/roles/{role_id}/permissions")
def get_role_permissions_endpoint(
    role_id: uuid.UUID,
    session: SessionDep,
    current_user: RequirePermission(PermissionType.MANAGE_ROLES) = None,
) -> Any:
    """
    Get role's permissions.
    """
    # Check if role exists
    role = session.get(Role, role_id)
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")

    permissions = get_role_permissions(session, role_id)
    return {"permissions": list(permissions)}


# Initialize default roles and permissions
@router.post("/initialize")
def initialize_rbac(
    session: SessionDep,
    current_user: RequireRole(RoleType.ADMIN) = None,
) -> Message:
    """
    Initialize default roles and permissions.
    """
    initialize_default_roles_and_permissions(session)
    return Message(message="RBAC system initialized successfully")
