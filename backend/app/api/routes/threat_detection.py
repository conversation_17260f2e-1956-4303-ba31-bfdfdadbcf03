import uuid
import cv2
import asyncio
import json
import base64
from datetime import datetime
from typing import Any, List

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from fastapi.responses import HTMLResponse
from sqlmodel import func, select

from app.api.deps import CurrentUser, SessionDep
from app.models import (
    ThreatDetection, 
    ThreatDetectionCreate, 
    ThreatDetectionPublic, 
    ThreatDetectionsPublic,
    ThreatDetectionUpdate,
    Message,
    DetectionFrame,
    ThreatAlert
)
from app.services.threat_detection import threat_detection_service

router = APIRouter(prefix="/threat-detection", tags=["threat-detection"])

# Store active WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except:
            self.disconnect(websocket)

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                disconnected.append(connection)
        
        # Remove disconnected clients
        for connection in disconnected:
            self.disconnect(connection)

manager = ConnectionManager()


@router.get("/", response_model=ThreatDetectionsPublic)
def read_threat_detections(
    session: SessionDep,
    current_user: CurrentUser,
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False
) -> Any:
    """
    Retrieve threat detections.
    """
    query = select(ThreatDetection)
    
    if active_only:
        query = query.where(ThreatDetection.is_active_threat == True)
    
    count_statement = select(func.count()).select_from(ThreatDetection)
    if active_only:
        count_statement = count_statement.where(ThreatDetection.is_active_threat == True)
    
    count = session.exec(count_statement).one()
    
    statement = query.offset(skip).limit(limit).order_by(ThreatDetection.detected_at.desc())
    threat_detections = session.exec(statement).all()
    
    return ThreatDetectionsPublic(data=threat_detections, count=count)


@router.get("/{threat_id}", response_model=ThreatDetectionPublic)
def read_threat_detection(
    threat_id: uuid.UUID,
    session: SessionDep,
    current_user: CurrentUser
) -> Any:
    """
    Get threat detection by ID.
    """
    threat_detection = session.get(ThreatDetection, threat_id)
    if not threat_detection:
        raise HTTPException(status_code=404, detail="Threat detection not found")
    return threat_detection


@router.patch("/{threat_id}", response_model=ThreatDetectionPublic)
def update_threat_detection(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    threat_id: uuid.UUID,
    threat_in: ThreatDetectionUpdate,
) -> Any:
    """
    Update a threat detection (e.g., mark as resolved).
    """
    threat_detection = session.get(ThreatDetection, threat_id)
    if not threat_detection:
        raise HTTPException(status_code=404, detail="Threat detection not found")
    
    update_dict = threat_in.model_dump(exclude_unset=True)
    
    # If marking as resolved, set resolved_at timestamp
    if update_dict.get('is_active_threat') == False and threat_detection.is_active_threat:
        update_dict['resolved_at'] = datetime.utcnow()
    
    threat_detection.sqlmodel_update(update_dict)
    session.add(threat_detection)
    session.commit()
    session.refresh(threat_detection)
    return threat_detection


@router.delete("/{threat_id}")
def delete_threat_detection(
    session: SessionDep,
    current_user: CurrentUser,
    threat_id: uuid.UUID
) -> Message:
    """
    Delete a threat detection.
    """
    threat_detection = session.get(ThreatDetection, threat_id)
    if not threat_detection:
        raise HTTPException(status_code=404, detail="Threat detection not found")
    
    session.delete(threat_detection)
    session.commit()
    return Message(message="Threat detection deleted successfully")


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket endpoint for real-time threat detection from camera feed.
    """
    await manager.connect(websocket)
    
    # Initialize camera
    cap = cv2.VideoCapture(0)  # Use 0 for default camera
    
    if not cap.isOpened():
        await websocket.send_text(json.dumps({"error": "Could not open camera"}))
        return
    
    camera_id = "default_camera"
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Perform threat detection
            annotated_frame, detections = threat_detection_service.detect_objects(frame)
            
            # Check for threats and save to database
            threats_detected = False
            for detection in detections:
                if detection.get('is_threat', False):
                    threats_detected = True
                    # Save threat to database
                    threat_id = await threat_detection_service.save_threat_detection(
                        camera_id, detection
                    )
                    
                    # Print threat alert to console
                    threat_detection_service.print_threat_alert(camera_id, [detection])
                    
                    # Broadcast threat alert to all connected clients
                    if threat_id:
                        alert = ThreatAlert(
                            threat_id=uuid.UUID(threat_id),
                            threat_type=detection['class'],
                            confidence=detection['confidence'],
                            camera_id=camera_id,
                            timestamp=datetime.now(),
                            bbox=detection['bbox'],
                            message=f"Threat detected: {detection['class']} with {detection['confidence']:.2f} confidence"
                        )
                        await manager.broadcast(json.dumps({
                            "type": "threat_alert",
                            "data": alert.model_dump(mode='json')
                        }))
            
            # Encode frame as base64
            frame_base64 = threat_detection_service.encode_frame_to_base64(annotated_frame)
            
            # Send frame and detections to client
            detection_frame = DetectionFrame(
                frame=frame_base64,
                detections=detections,
                timestamp=asyncio.get_event_loop().time(),
                threats_detected=threats_detected
            )
            
            await websocket.send_text(json.dumps({
                "type": "detection_frame",
                "data": detection_frame.model_dump()
            }))
            
            # Control frame rate (~10 FPS)
            await asyncio.sleep(0.1)
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        print(f"Error in WebSocket: {e}")
        await websocket.send_text(json.dumps({"error": str(e)}))
    finally:
        cap.release()


@router.get("/interface/", response_class=HTMLResponse)
async def get_threat_detection_interface():
    """
    Serve the threat detection web interface.
    """
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html>
    <head>
        <title>🛡️ Threat Detection System</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                max-width: 1400px;
                margin: 0 auto;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
            }
            .container {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                padding: 30px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            h1 {
                text-align: center;
                color: #fff;
                margin-bottom: 30px;
                font-size: 2.5em;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            }
            .video-container {
                text-align: center;
                margin-bottom: 20px;
                position: relative;
            }
            #videoFeed {
                max-width: 100%;
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            }
            .controls {
                text-align: center;
                margin: 20px 0;
            }
            button {
                background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 25px;
                cursor: pointer;
                margin: 0 10px;
                font-size: 16px;
                font-weight: bold;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }
            button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            }
            button:disabled {
                background: #666;
                cursor: not-allowed;
                transform: none;
            }
            .status {
                text-align: center;
                margin: 15px 0;
                padding: 15px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 1.1em;
            }
            .status.connected {
                background: rgba(46, 204, 113, 0.3);
                border: 2px solid #2ecc71;
            }
            .status.disconnected {
                background: rgba(231, 76, 60, 0.3);
                border: 2px solid #e74c3c;
            }
            .status.threat {
                background: rgba(255, 107, 107, 0.4);
                border: 2px solid #ff6b6b;
                animation: pulse 1s infinite;
            }
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.7; }
                100% { opacity: 1; }
            }
            .detections {
                margin-top: 20px;
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
            }
            .detection-panel {
                background: rgba(255, 255, 255, 0.1);
                padding: 20px;
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .detection-item {
                background: rgba(255, 255, 255, 0.1);
                padding: 12px;
                margin: 8px 0;
                border-radius: 8px;
                border-left: 4px solid #2ecc71;
                transition: all 0.3s ease;
            }
            .detection-item.threat {
                border-left-color: #ff6b6b;
                background: rgba(255, 107, 107, 0.2);
                animation: threatGlow 2s infinite;
            }
            @keyframes threatGlow {
                0%, 100% { box-shadow: 0 0 5px rgba(255, 107, 107, 0.5); }
                50% { box-shadow: 0 0 20px rgba(255, 107, 107, 0.8); }
            }
            .threat-alerts {
                max-height: 300px;
                overflow-y: auto;
            }
            .alert-item {
                background: rgba(255, 107, 107, 0.3);
                border: 1px solid #ff6b6b;
                padding: 15px;
                margin: 10px 0;
                border-radius: 10px;
                animation: slideIn 0.5s ease;
            }
            @keyframes slideIn {
                from { transform: translateX(-100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🛡️ YOLOv8 Threat Detection System</h1>
            
            <div class="status" id="status">Disconnected</div>
            
            <div class="controls">
                <button id="startBtn" onclick="startDetection()">🚀 Start Detection</button>
                <button id="stopBtn" onclick="stopDetection()" disabled>🛑 Stop Detection</button>
            </div>
            
            <div class="video-container">
                <img id="videoFeed" src="" alt="Video Feed" style="display: none;">
            </div>
            
            <div class="detections">
                <div class="detection-panel">
                    <h3>🔍 Current Detections</h3>
                    <div id="detectionsList"></div>
                </div>
                
                <div class="detection-panel">
                    <h3>🚨 Threat Alerts</h3>
                    <div id="threatAlerts" class="threat-alerts"></div>
                </div>
            </div>
        </div>

        <script>
            let ws = null;
            let isConnected = false;

            function updateStatus(status, message) {
                const statusEl = document.getElementById('status');
                const startBtn = document.getElementById('startBtn');
                const stopBtn = document.getElementById('stopBtn');
                
                statusEl.className = `status ${status}`;
                statusEl.textContent = message;
                
                if (status === 'connected') {
                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                    isConnected = true;
                } else {
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                    isConnected = false;
                    document.getElementById('videoFeed').style.display = 'none';
                }
            }

            function startDetection() {
                ws = new WebSocket('ws://localhost:8000/api/v1/threat-detection/ws');
                
                ws.onopen = function(event) {
                    updateStatus('connected', '🟢 Connected - Monitoring for threats...');
                    console.log('WebSocket connected');
                };
                
                ws.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        
                        if (message.error) {
                            alert('Error: ' + message.error);
                            return;
                        }
                        
                        if (message.type === 'detection_frame') {
                            handleDetectionFrame(message.data);
                        } else if (message.type === 'threat_alert') {
                            handleThreatAlert(message.data);
                        }
                        
                    } catch (e) {
                        console.error('Error parsing message:', e);
                    }
                };
                
                ws.onclose = function(event) {
                    updateStatus('disconnected', '🔴 Disconnected');
                    console.log('WebSocket disconnected');
                };
                
                ws.onerror = function(error) {
                    console.error('WebSocket error:', error);
                    updateStatus('disconnected', '❌ Connection Error');
                };
            }

            function stopDetection() {
                if (ws) {
                    ws.close();
                    ws = null;
                }
                updateStatus('disconnected', '🔴 Disconnected');
                document.getElementById('detectionsList').innerHTML = '';
            }

            function handleDetectionFrame(data) {
                // Update video feed
                const videoFeed = document.getElementById('videoFeed');
                videoFeed.src = 'data:image/jpeg;base64,' + data.frame;
                videoFeed.style.display = 'block';
                
                // Update status based on threats
                if (data.threats_detected) {
                    updateStatus('threat', '🚨 THREAT DETECTED!');
                } else if (isConnected) {
                    updateStatus('connected', '🟢 Connected - Monitoring for threats...');
                }
                
                // Update detections list
                updateDetections(data.detections);
            }

            function handleThreatAlert(alertData) {
                const alertsContainer = document.getElementById('threatAlerts');
                
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert-item';
                alertDiv.innerHTML = `
                    <strong>🚨 ${alertData.threat_type.toUpperCase()}</strong><br>
                    <small>Confidence: ${(alertData.confidence * 100).toFixed(1)}%</small><br>
                    <small>Time: ${new Date(alertData.timestamp).toLocaleTimeString()}</small><br>
                    <small>Camera: ${alertData.camera_id}</small>
                `;
                
                alertsContainer.insertBefore(alertDiv, alertsContainer.firstChild);
                
                // Keep only last 10 alerts
                while (alertsContainer.children.length > 10) {
                    alertsContainer.removeChild(alertsContainer.lastChild);
                }
            }

            function updateDetections(detections) {
                const listEl = document.getElementById('detectionsList');
                
                if (detections.length === 0) {
                    listEl.innerHTML = '<p>No objects detected</p>';
                    return;
                }
                
                listEl.innerHTML = detections.map(detection => `
                    <div class="detection-item ${detection.is_threat ? 'threat' : ''}">
                        <strong>${detection.is_threat ? '🚨 ' : ''}${detection.class}</strong><br>
                        <small>Confidence: ${(detection.confidence * 100).toFixed(1)}%</small><br>
                        <small>Position: [${detection.bbox.join(', ')}]</small>
                        ${detection.is_threat ? '<br><small style="color: #ff6b6b;">⚠️ THREAT DETECTED</small>' : ''}
                    </div>
                `).join('');
            }

            // Initialize
            updateStatus('disconnected', '🔴 Disconnected');
        </script>
    </body>
    </html>
    """)
