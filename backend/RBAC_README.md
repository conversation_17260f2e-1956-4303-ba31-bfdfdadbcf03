# Role-Based Access Control (RBAC) System

This document describes the RBAC system implemented for the school security application.

## Overview

The RBAC system provides fine-grained access control with predefined roles and permissions. It supports:

- **5 Predefined Roles**: Admin, Teachers, Management, Security, IT
- **Granular Permissions**: 13 different permissions covering all system operations
- **Flexible Assignment**: Users can have multiple roles
- **API Integration**: Easy-to-use decorators and dependencies for FastAPI endpoints

## Roles and Default Permissions

### Admin
- Full system access
- All permissions granted by default

### Teachers
- `READ_USER` - View user information
- `READ_SCHOOL` - View school information
- `VIEW_THREATS` - View threat detection alerts
- `MANAGE_CLASSES` - Manage class information
- `VIEW_STUDENTS` - View student information
- `MANAGE_CURRICULUM` - Manage curriculum content

### Management
- `READ_USER` - View user information
- `UPDATE_USER` - Update user information
- `READ_SCHOOL` - View school information
- `UPDATE_SCHOOL` - Update school information
- `VIEW_THREATS` - View threat detection alerts
- `<PERSON><PERSON><PERSON>_THREATS` - Manage threat responses
- `VIEW_LOGS` - View system logs
- `MANAGE_CLASSES` - Manage class information
- `VIEW_STUDENTS` - View student information

### Security
- `READ_USER` - View user information
- `READ_SCHOOL` - View school information
- `VIEW_THREATS` - View threat detection alerts
- `MANAGE_THREATS` - Manage threat responses
- `CONFIGURE_CAMERAS` - Configure camera settings
- `VIEW_LOGS` - View system logs

### IT
- `READ_USER` - View user information
- `UPDATE_USER` - Update user information
- `READ_SCHOOL` - View school information
- `UPDATE_SCHOOL` - Update school information
- `VIEW_THREATS` - View threat detection alerts
- `CONFIGURE_CAMERAS` - Configure camera settings
- `SYSTEM_CONFIG` - Configure system settings
- `VIEW_LOGS` - View system logs

## API Usage

### Using Permission-Based Access Control

```python
from app.api.deps import require_permission
from app.models import PermissionType

@router.get("/users/")
def read_users(
    session: SessionDep,
    current_user: CurrentUser = Depends(require_permission(PermissionType.READ_USER))
):
    # Only users with READ_USER permission can access this endpoint
    pass
```

### Using Role-Based Access Control

```python
from app.api.deps import require_role, require_any_role
from app.models import RoleType

@router.post("/admin-only/")
def admin_only_endpoint(
    current_user: CurrentUser = Depends(require_role(RoleType.ADMIN))
):
    # Only users with ADMIN role can access this endpoint
    pass

@router.get("/security-or-management/")
def security_management_endpoint(
    current_user: CurrentUser = Depends(require_any_role([RoleType.SECURITY, RoleType.MANAGEMENT]))
):
    # Users with either SECURITY or MANAGEMENT role can access this endpoint
    pass
```

## RBAC Management API Endpoints

### Role Management
- `GET /api/v1/rbac/roles/` - List all roles
- `POST /api/v1/rbac/roles/` - Create a new role
- `GET /api/v1/rbac/roles/{role_id}` - Get role by ID
- `PATCH /api/v1/rbac/roles/{role_id}` - Update role
- `DELETE /api/v1/rbac/roles/{role_id}` - Delete role

### Permission Management
- `GET /api/v1/rbac/permissions/` - List all permissions
- `POST /api/v1/rbac/permissions/` - Create a new permission

### User Role Assignment
- `POST /api/v1/rbac/users/{user_id}/roles` - Assign role to user
- `DELETE /api/v1/rbac/users/{user_id}/roles/{role_id}` - Remove role from user
- `GET /api/v1/rbac/users/{user_id}/roles` - Get user's roles
- `GET /api/v1/rbac/users/{user_id}/permissions` - Get user's permissions

### Role Permission Assignment
- `POST /api/v1/rbac/roles/{role_id}/permissions` - Assign permission to role
- `DELETE /api/v1/rbac/roles/{role_id}/permissions/{permission_id}` - Remove permission from role
- `GET /api/v1/rbac/roles/{role_id}/permissions` - Get role's permissions

### System Initialization
- `POST /api/v1/rbac/initialize` - Initialize default roles and permissions

## Database Models

### Core Models
- `Role` - Stores role information
- `Permission` - Stores permission information
- `UserRole` - Many-to-many relationship between users and roles
- `RolePermission` - Many-to-many relationship between roles and permissions

### Enums
- `RoleType` - Predefined role types
- `PermissionType` - Predefined permission types

## Programmatic Usage

### Check User Permissions

```python
from app.core.rbac import user_has_permission, user_has_role
from app.models import PermissionType, RoleType

# Check if user has specific permission
if user_has_permission(session, user_id, PermissionType.CREATE_USER):
    # User can create users
    pass

# Check if user has specific role
if user_has_role(session, user_id, RoleType.ADMIN):
    # User is an admin
    pass
```

### Assign Roles and Permissions

```python
from app.core.rbac import assign_role_to_user, assign_permission_to_role

# Assign role to user
assign_role_to_user(session, user_id, role_id, assigned_by_user_id)

# Assign permission to role
assign_permission_to_role(session, role_id, permission_id)
```

## Initialization

The RBAC system is automatically initialized when the application starts:

1. Default roles and permissions are created
2. Role-permission mappings are established
3. The first superuser is assigned the Admin role

To manually initialize or reinitialize the system:

```bash
# Run the initialization script
python -m app.initial_data

# Or use the API endpoint (requires Admin role)
POST /api/v1/rbac/initialize
```

## Security Considerations

1. **Superuser Override**: Superusers bypass all RBAC checks
2. **Self-Access**: Users can always access their own profile information
3. **Role Hierarchy**: No built-in role hierarchy; permissions are explicit
4. **Audit Trail**: Role assignments track who assigned them and when

## Migration from Simple Superuser System

The RBAC system maintains backward compatibility:

- Existing `is_superuser` checks still work
- Superusers automatically have all permissions
- Gradual migration to permission-based checks is supported

## Example Usage

See `backend/app/examples/rbac_example.py` for a complete demonstration of the RBAC system.
