# 🛡️ YOLOv8 Threat Detection System

A real-time threat detection system using YOLOv8 for camera feed analysis with FastAPI backend and WebSocket communication.

## 🚀 Features

- **Real-time Object Detection**: Uses YOLOv8 for accurate object detection
- **Threat Classification**: Automatically identifies potential threats
- **Live Camera Feed**: WebSocket-based real-time video streaming
- **Alert System**: Console alerts and database logging for threats
- **Web Interface**: Beautiful, responsive web interface for monitoring
- **Database Integration**: SQLModel/PostgreSQL for threat logging
- **RESTful API**: Full CRUD operations for threat management

## 🎯 Threat Detection Capabilities

The system can detect and classify the following as potential threats:

- **Person**: Unauthorized access detection
- **Knife**: Sharp weapon detection
- **Scissors**: Sharp object detection
- **Bottle**: Potential weapon detection
- **Baseball bat**: Blunt weapon detection
- **Sports ball**: Thrown object detection

## 📋 Prerequisites

- Python 3.10+
- PostgreSQL database
- Camera/webcam (for live detection)
- UV package manager

## 🛠️ Installation

The required dependencies are already added to the project:

```bash
# Dependencies added via UV
ultralytics>=8.3.143    # YOLOv8
opencv-python-headless>=*********  # Computer vision
pillow>=11.2.1          # Image processing
numpy>=2.2.6            # Numerical operations
```

## 🚀 Quick Start

### 1. Run Database Migrations

```bash
cd backend
uv run alembic upgrade head
```

### 2. Test the System

```bash
cd backend
uv run python test_threat_detection.py
```

### 3. Start the FastAPI Server

```bash
cd backend
uv run fastapi dev app/main.py --host 0.0.0.0 --port 8000
```

### 4. Access the Web Interface

Open your browser and navigate to:
```
http://localhost:8000/api/v1/threat-detection/interface/
```

### 5. Start Detection

1. Click the "🚀 Start Detection" button
2. Allow camera access when prompted
3. The system will begin real-time threat detection
4. Threats will be highlighted in red with alerts

## 📡 API Endpoints

### WebSocket Endpoint
- `ws://localhost:8000/api/v1/threat-detection/ws` - Real-time detection feed

### REST API Endpoints
- `GET /api/v1/threat-detection/` - List all threat detections
- `GET /api/v1/threat-detection/{threat_id}` - Get specific threat
- `PATCH /api/v1/threat-detection/{threat_id}` - Update threat (mark as resolved)
- `DELETE /api/v1/threat-detection/{threat_id}` - Delete threat record
- `GET /api/v1/threat-detection/interface/` - Web interface

### Query Parameters
- `active_only=true` - Filter only active threats
- `skip=0&limit=100` - Pagination

## 🔧 Configuration

### Threat Detection Settings

You can modify threat detection settings in `app/services/threat_detection.py`:

```python
# Confidence thresholds
self.threat_confidence_threshold = 0.6  # Minimum confidence for threats
self.detection_confidence_threshold = 0.5  # Minimum confidence for detection

# Add custom threat classes
self.threat_classes = {
    'person': 0,
    'knife': 43,
    'custom_threat': 999,  # Add your custom threats
}
```

### Camera Configuration

By default, the system uses camera index 0. To use a different camera:

```python
# In threat_detection.py WebSocket endpoint
cap = cv2.VideoCapture(1)  # Change to your camera index
```

## 📊 Database Schema

The system creates a `threatdetection` table with the following fields:

- `id` (UUID) - Primary key
- `camera_id` (String) - Camera identifier
- `threat_type` (String) - Type of threat detected
- `confidence` (Float) - Detection confidence (0.0-1.0)
- `bbox_x1, bbox_y1, bbox_x2, bbox_y2` (Integer) - Bounding box coordinates
- `image_path` (String, Optional) - Path to saved image
- `is_active_threat` (Boolean) - Whether threat is still active
- `detected_at` (DateTime) - When threat was detected
- `resolved_at` (DateTime, Optional) - When threat was resolved

## 🚨 Alert System

When threats are detected, the system:

1. **Console Alerts**: Prints formatted threat alerts to console
2. **Database Logging**: Saves threat details to PostgreSQL
3. **WebSocket Broadcasting**: Sends real-time alerts to connected clients
4. **Visual Indicators**: Highlights threats in red on video feed

Example console alert:
```
==================================================
🚨 THREAT ALERT 🚨
Camera ID: default_camera
Timestamp: 2025-05-23 11:55:59
--------------------------------------------------
Threat Type: person
Confidence: 0.85
Location: [100, 100, 200, 300]
------------------------------
==================================================
```

## 🎨 Web Interface Features

- **Real-time Video Feed**: Live camera stream with threat highlighting
- **Detection Panel**: Shows all current detections
- **Threat Alerts Panel**: Displays recent threat alerts
- **Status Indicators**: Connection status and threat warnings
- **Responsive Design**: Works on desktop and mobile devices
- **Dark Theme**: Modern, professional appearance

## 🔒 Security Considerations

- The system requires authentication (CurrentUser dependency)
- WebSocket connections are managed securely
- Database operations use SQLModel for SQL injection protection
- Camera access requires user permission

## 🧪 Testing

Run the comprehensive test suite:

```bash
cd backend
uv run python test_threat_detection.py
```

The test suite verifies:
- YOLOv8 model loading
- Object detection pipeline
- Threat classification logic
- Frame encoding/decoding
- Alert system functionality

## 📈 Performance

- **Frame Rate**: ~10 FPS (configurable)
- **Detection Speed**: ~70ms per frame (depends on hardware)
- **Model Size**: YOLOv8n (~6MB) for fast inference
- **Memory Usage**: Optimized for real-time processing

## 🔧 Troubleshooting

### Camera Not Found
```bash
# Check available cameras
python -c "import cv2; print([i for i in range(10) if cv2.VideoCapture(i).isOpened()])"
```

### Model Download Issues
The YOLOv8 model downloads automatically on first use. If issues occur:
```bash
# Manually download model
python -c "from ultralytics import YOLO; YOLO('yolov8n.pt')"
```

### WebSocket Connection Issues
- Ensure FastAPI server is running
- Check firewall settings
- Verify camera permissions in browser

## 🚀 Production Deployment

For production use:

1. Use a larger YOLOv8 model (yolov8s.pt, yolov8m.pt) for better accuracy
2. Implement proper authentication and authorization
3. Add SSL/TLS for WebSocket connections
4. Configure proper logging and monitoring
5. Set up database backups
6. Implement rate limiting for API endpoints

## 📝 License

This threat detection system is part of the FastAPI project template and follows the same licensing terms.

## 🤝 Contributing

To contribute to the threat detection system:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📞 Support

For issues related to the threat detection system, please check:

1. The test suite output for diagnostic information
2. FastAPI server logs for error details
3. Browser console for WebSocket connection issues
4. Camera permissions and availability
