#!/usr/bin/env python3
"""
Script to publish webcam feed to RTSP server
This script captures video from your local webcam and publishes it to the RTSP server
"""

import cv2
import subprocess
import sys
import time
import argparse
from pathlib import Path

def check_ffmpeg():
    """Check if ffmpeg is available"""
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def publish_webcam_opencv(rtsp_url: str, camera_index: int = 0):
    """
    Publish webcam using OpenCV (simpler but less efficient)
    """
    print(f"Starting webcam capture from camera {camera_index}")
    print(f"Publishing to: {rtsp_url}")
    print("Press 'q' to quit")

    cap = cv2.VideoCapture(camera_index)

    if not cap.isOpened():
        print(f"Error: Could not open camera {camera_index}")
        return False

    # Set camera properties
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                print("Error: Could not read frame")
                break

            # Display frame locally (optional)
            cv2.imshow('Webcam Feed', frame)

            # Note: OpenCV doesn't directly support RTSP publishing
            # This is just for local preview. For actual RTSP publishing, use ffmpeg method

            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

    except KeyboardInterrupt:
        print("\nStopping webcam capture...")
    finally:
        cap.release()
        cv2.destroyAllWindows()

    return True

def publish_webcam_ffmpeg(rtsp_url: str, camera_index: int = 0):
    """
    Publish webcam using ffmpeg (recommended for RTSP streaming)
    """
    if not check_ffmpeg():
        print("Error: ffmpeg is not installed or not in PATH")
        print("Please install ffmpeg to use RTSP streaming")
        return False

    print(f"Starting webcam capture from camera {camera_index}")
    print(f"Publishing to: {rtsp_url}")
    print("Press Ctrl+C to stop")

    # Determine video input based on platform
    if sys.platform == "darwin":  # macOS
        video_input = f"{camera_index}:none"
        input_format = "avfoundation"
    elif sys.platform.startswith("linux"):  # Linux
        video_input = f"/dev/video{camera_index}"
        input_format = "v4l2"
    elif sys.platform == "win32":  # Windows
        video_input = f"video=USB2.0 Camera"  # Adjust based on your camera name
        input_format = "dshow"
    else:
        print(f"Unsupported platform: {sys.platform}")
        return False

    # FFmpeg command to capture webcam and stream to RTSP
    if sys.platform == "darwin":  # macOS specific settings
        cmd = [
            'ffmpeg',
            '-f', input_format,
            '-framerate', '30',
            '-video_size', '640x480',
            '-pixel_format', 'uyvy422',
            '-i', str(camera_index),
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-g', '30',
            '-f', 'rtsp',
            rtsp_url
        ]
    else:
        cmd = [
            'ffmpeg',
            '-f', input_format,
            '-i', video_input,
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-r', '30',
            '-s', '640x480',
            '-f', 'rtsp',
            rtsp_url
        ]

    try:
        print(f"Running command: {' '.join(cmd)}")
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error running ffmpeg: {e}")
        return False
    except KeyboardInterrupt:
        print("\nStopping webcam stream...")

    return True

def main():
    parser = argparse.ArgumentParser(description='Publish webcam to RTSP server')
    parser.add_argument('--camera', '-c', type=int, default=0,
                       help='Camera index (default: 0)')
    parser.add_argument('--rtsp-url', '-u', type=str,
                       default='rtsp://localhost:8554/webcam',
                       help='RTSP URL to publish to (default: rtsp://localhost:8554/webcam)')
    parser.add_argument('--method', '-m', choices=['ffmpeg', 'opencv'],
                       default='ffmpeg',
                       help='Method to use for streaming (default: ffmpeg)')

    args = parser.parse_args()

    print("=== Webcam to RTSP Publisher ===")
    print(f"Camera: {args.camera}")
    print(f"RTSP URL: {args.rtsp_url}")
    print(f"Method: {args.method}")
    print()

    if args.method == 'ffmpeg':
        success = publish_webcam_ffmpeg(args.rtsp_url, args.camera)
    else:
        print("Note: OpenCV method only shows local preview, not actual RTSP streaming")
        success = publish_webcam_opencv(args.rtsp_url, args.camera)

    if success:
        print("Webcam publishing completed successfully")
    else:
        print("Webcam publishing failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
