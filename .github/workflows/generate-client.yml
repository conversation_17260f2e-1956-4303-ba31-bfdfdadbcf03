name: Generate Client

on:
  pull_request:
    types:
    - opened
    - synchronize

jobs:
  generate-client:
    permissions:
      contents: write
    runs-on: ubuntu-latest
    steps:
    # For PRs from forks
    - uses: actions/checkout@v4
    # For PRs from the same repo
    - uses: actions/checkout@v4
      if: ( github.event_name != 'pull_request' || github.secret_source == 'Actions' )
      with:
        ref: ${{ github.head_ref }}
        token: ${{ secrets.FULL_STACK_FASTAPI_TEMPLATE_REPO_TOKEN }}
    - uses: actions/setup-node@v4
      with:
        node-version: lts/*
    - uses: actions/setup-python@v5
      with:
        python-version: "3.10"
    - name: Install uv
      uses: astral-sh/setup-uv@v6
      with:
        version: "0.4.15"
        enable-cache: true
    - name: Install dependencies
      run: npm ci
      working-directory: frontend
    - run: uv sync
      working-directory: backend
    - run: uv run bash scripts/generate-client.sh
      env:
        VIRTUAL_ENV: backend/.venv
        SECRET_KEY: just-for-generating-client
        POSTGRES_PASSWORD: just-for-generating-client
        FIRST_SUPERUSER_PASSWORD: just-for-generating-client
    - name: Add changes to git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "github-actions"
        git add frontend/src/client
    # Same repo PRs
    - name: Push changes
      if: ( github.event_name != 'pull_request' || github.secret_source == 'Actions' )
      run: |
        git diff --staged --quiet || git commit -m "✨ Autogenerate frontend client"
        git push
    # Fork PRs
    - name: Check changes
      if: ( github.event_name == 'pull_request' && github.secret_source != 'Actions' )
      run: |
        git diff --staged --quiet || (echo "Changes detected in generated client, run scripts/generate-client.sh and commit the changes" && exit 1)
