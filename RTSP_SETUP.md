# 📹 RTSP Streaming Setup Guide

This guide explains how to set up RTSP streaming for your object detection application.

## 🚨 Problem Solved

The error `OpenCV: Couldn't read video stream from file "rtsp://localhost:8554/webcam"` occurs because there's no RTSP server running to provide the video stream.

## 🛠️ Solutions Implemented

### Solution 1: Docker-based RTSP Server (Recommended)

We've added a MediaMTX RTSP server to your Docker Compose setup.

#### Setup Steps:

1. **Start the RTSP server:**
   ```bash
   docker-compose up rtsp-server -d
   ```

2. **Verify the server is running:**
   ```bash
   docker-compose ps
   # You should see rtsp-server running on ports 8554, 8888, 8889
   ```

3. **Publish your webcam to the RTSP server:**
   
   **Option A: Using FFmpeg (Recommended)**
   ```bash
   # Install ffmpeg if not already installed
   # macOS: brew install ffmpeg
   # Ubuntu: sudo apt install ffmpeg
   # Windows: Download from https://ffmpeg.org/
   
   # Run the webcam publisher script
   python scripts/publish_webcam.py --method ffmpeg
   ```
   
   **Option B: Using OBS Studio**
   - Install OBS Studio
   - Add your webcam as a source
   - Go to Settings > Stream
   - Set Service to "Custom"
   - Set Server to: `rtsp://localhost:8554/webcam`
   - Start streaming

4. **Test the RTSP stream:**
   ```bash
   # Using ffplay (comes with ffmpeg)
   ffplay rtsp://localhost:8554/webcam
   
   # Or using VLC media player
   # Open VLC > Media > Open Network Stream > rtsp://localhost:8554/webcam
   ```

### Solution 2: Fallback to Local Camera (Already Implemented)

The ingestion service now automatically falls back to your local camera (index 0) if the RTSP stream is not available.

#### Environment Variables:

You can configure the video source using environment variables:

```bash
# In your .env file or environment
RTSP_URL=rtsp://localhost:8554/webcam
FALLBACK_CAMERA=0
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
```

## 🔧 Configuration Files

### RTSP Server Configuration (`rtsp-config.yml`)
- Configures MediaMTX server settings
- Defines the `/webcam` path for your stream
- Includes a test pattern at `/test` for debugging

### Docker Compose Updates
- Added `rtsp-server` service using `bluenviron/mediamtx`
- Exposed ports: 8554 (RTSP), 8888 (WebRTC), 8889 (API)

## 🚀 Usage Examples

### 1. Start Everything with Docker
```bash
# Start all services including RTSP server
docker-compose up -d

# Check logs
docker-compose logs rtsp-server
```

### 2. Publish Webcam Stream
```bash
# Using the provided script
python scripts/publish_webcam.py

# Or manually with ffmpeg
ffmpeg -f avfoundation -i "0:none" -c:v libx264 -preset ultrafast -tune zerolatency -f rtsp rtsp://localhost:8554/webcam
```

### 3. Run Ingestion Service
```bash
cd backend
python app/services/ingestion_service.py
```

### 4. Test with Threat Detection WebSocket
```bash
# Start your FastAPI backend
cd backend
uvicorn app.main:app --reload

# Open the threat detection interface
# http://localhost:8000/api/v1/threat-detection/demo
```

## 🐛 Troubleshooting

### RTSP Server Not Starting
```bash
# Check if port 8554 is already in use
lsof -i :8554

# Check Docker logs
docker-compose logs rtsp-server
```

### Camera Not Found
```bash
# List available cameras (Linux)
ls /dev/video*

# Test camera directly
python -c "import cv2; cap = cv2.VideoCapture(0); print('Camera available:', cap.isOpened()); cap.release()"
```

### FFmpeg Issues
```bash
# Check ffmpeg installation
ffmpeg -version

# List available input devices
# macOS
ffmpeg -f avfoundation -list_devices true -i ""

# Linux
ffmpeg -f v4l2 -list_devices true -i ""
```

### Network Issues
```bash
# Test RTSP connectivity
telnet localhost 8554

# Check if stream is publishing
curl http://localhost:8889/v3/paths/list
```

## 📊 Monitoring

### RTSP Server API
The MediaMTX server provides a REST API on port 8889:

```bash
# List all paths
curl http://localhost:8889/v3/paths/list

# Get path info
curl http://localhost:8889/v3/paths/get/webcam

# Get server stats
curl http://localhost:8889/v3/config/global/get
```

### Logs
```bash
# View ingestion service logs
python app/services/ingestion_service.py

# View RTSP server logs
docker-compose logs -f rtsp-server
```

## 🔄 Alternative Solutions

If you prefer not to use RTSP, you can:

1. **Use direct camera access** (already implemented as fallback)
2. **Use IP camera streams** (modify RTSP_URL to your IP camera)
3. **Use video files** (modify the video source in ingestion_service.py)

## 📝 Notes

- The ingestion service now gracefully handles RTSP unavailability
- Kafka integration is optional (will work without Kafka)
- The system automatically reconnects if the video source becomes unavailable
- Frame rate is controlled to ~30 FPS to avoid overwhelming the system
